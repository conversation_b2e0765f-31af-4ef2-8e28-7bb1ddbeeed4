/**
 * React hook for initializing Zenoh client and subscriptions
 */

import { useEffect } from 'react';
import { useZenohStore, initializeSystemSubscriptions } from '../stores/zenoh-store';

export const useZenohInitialization = (config?: any) => {
  const { client, initializeClient, connect, connectionStatus } = useZenohStore();

  useEffect(() => {
    if (!client) {
      // Initialize the Zenoh client
      initializeClient(config);
    }
  }, [client, initializeClient, config]);

  useEffect(() => {
    if (client && connectionStatus.state === 'disconnected') {
      // Auto-connect when client is initialized
      connect().catch(error => {
        console.error('Failed to connect to Zenoh:', error);
      });
    }
  }, [client, connect, connectionStatus.state]);

  useEffect(() => {
    if (client && connectionStatus.state === 'connected') {
      // Initialize system subscriptions when connected
      initializeSystemSubscriptions();
    }
  }, [client, connectionStatus.state]);

  return {
    client,
    connectionStatus,
    isConnected: connectionStatus.state === 'connected'
  };
};