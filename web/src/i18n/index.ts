import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import zhCN from '../locales/zh-CN';

// 创建英文翻译资源（作为默认语言）
const enUS = {
  // 通用
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    refresh: 'Refresh',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    // 操作按钮
    start: 'Start',
    stop: 'Stop',
    pause: 'Pause',
    resume: 'Resume',
    reset: 'Reset',
    apply: 'Apply',
    submit: 'Submit',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    // 状态文本
    enabled: 'Enabled',
    disabled: 'Disabled',
    active: 'Active',
    inactive: 'Inactive',
    available: 'Available',
    unavailable: 'Unavailable',
    // 时间相关
    now: 'Now',
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    // 数据单位
    bytes: 'Bytes',
    kb: 'KB',
    mb: 'MB',
    gb: 'GB',
    tb: 'TB',
    // 表格操作
    actions: 'Actions',
    view: 'View',
    download: 'Download',
    export: 'Export',
    import: 'Import',
    // 表单验证
    required: 'Required',
    invalid: 'Invalid',
    tooShort: 'Too Short',
    tooLong: 'Too Long',
  },

  // 菜单
  menu: {
    dashboard: 'Dashboard',
    training: 'Training',
    simulation: 'Simulation',
    deployment: 'Deployment',
    monitoring: 'Monitoring',
    settings: 'Settings',
  },

  // 状态
  status: {
    online: 'Online',
    offline: 'Offline',
    running: 'Running',
    stopped: 'Stopped',
    paused: 'Paused',
    completed: 'Completed',
    failed: 'Failed',
    connecting: 'Connecting',
    connected: 'Connected',
    disconnected: 'Disconnected',
  },

  // 训练相关
  training: {
    startTraining: 'Start Training',
    stopTraining: 'Stop Training',
    pauseTraining: 'Pause Training',
    resumeTraining: 'Resume Training',
    trainingProgress: 'Training Progress',
    trainingJobs: 'Training Jobs',
    trainingConfig: 'Training Configuration',
    modelName: 'Model Name',
    episodes: 'Episodes',
    learningRate: 'Learning Rate',
    batchSize: 'Batch Size',
    trainingManagement: 'Training Management',
    currentStatus: 'Current Status',
    progress: 'Progress',
    currentIteration: 'Current Iteration',
    bestReward: 'Best Reward',
    taskName: 'Task Name',
    numEnvironments: 'Number of Environments',
    maxIterations: 'Max Iterations',
    // 训练配置
    trainingConfiguration: 'Training Configuration',
    simple: 'Simple',
    advanced: 'Advanced',
    pleaseSelectTask: 'Please select a task!',
    pleaseInputNumEnvs: 'Please input number of environments!',
    pleaseInputMaxIterations: 'Please input max iterations!',
    pleaseInputLearningRate: 'Please input learning rate!',
    // 任务选项
    anymalCRoughTerrain: 'Anymal-C Rough Terrain',
    anymalCFlat: 'Anymal-C Flat',
    anymalCStairs: 'Anymal-C Stairs',
    anymalCExtreme: 'Anymal-C Extreme Terrain',
    zqsa01RoughTerrain: 'ZQSA01 Rough Terrain',
    // 高级设置
    advancedSettings: 'Advanced Settings',
    experimentName: 'Experiment Name',
    runName: 'Run Name',
    resumeFromCheckpoint: 'Resume from Checkpoint',
    checkpoint: 'Checkpoint',
    // 训练状态
    trainingActive: 'Training Active',
    trainingIdle: 'Idle',
    trainingPaused: 'Paused',
    trainingCompleted: 'Completed',
    trainingFailed: 'Failed',
    // 训练指标
    totalReward: 'Total Reward',
    episodeLength: 'Episode Length',
    successRate: 'Success Rate',
    averageReward: 'Average Reward',
    // 训练任务表格
    id: 'ID',
    startTime: 'Start Time',
    duration: 'Duration',
    reward: 'Reward',
    actions: 'Actions',
    view: 'View',
    stop: 'Stop',
    pause: 'Pause',
    resume: 'Resume',
  },

  // 系统监控
  monitoring: {
    systemMetrics: 'System Metrics',
    cpuUsage: 'CPU Usage',
    gpuUsage: 'GPU Usage',
    memoryUsage: 'Memory Usage',
    networkTraffic: 'Network Traffic',
    diskUsage: 'Disk Usage',
    systemMonitoring: 'System Monitoring & Logs',
    systemOverview: 'System Overview',
    serviceHealth: 'Service Health Status',
    systemLogs: 'System Logs',
    activeAlerts: 'Active Alerts',
    errorMonitoring: 'Error Monitoring',
  },

  // 仿真相关
  simulation: {
    startSimulation: 'Start Simulation',
    stopSimulation: 'Stop Simulation',
    simulationConfig: 'Simulation Configuration',
    environment: 'Environment',
    robotModel: 'Robot Model',
    physics: 'Physics',
    simulationRobotControl: 'Simulation & Robot Control',
    robotControl: 'Robot Control',
    robotState: 'Robot State',
    advancedConfig: 'Advanced Config',
    terrainType: 'Terrain Type',
    simulationStatus: 'Simulation Status',
    applyConfig: 'Apply Configuration',
    // 环境配置
    environmentConfiguration: 'Environment Configuration',
    flatGround: 'Flat Ground',
    roughTerrain: 'Rough Terrain',
    stairs: 'Stairs',
    slope: 'Slope',
    randomObstacles: 'Random Obstacles',
    terrainLength: 'Terrain Length',
    terrainWidth: 'Terrain Width',
    gravity: 'Gravity',
    timeStep: 'Time Step',
    // 机器人控制
    robotControlPanel: 'Robot Control Panel',
    emergencyStop: 'Emergency Stop',
    emergencyStopActivated: 'Emergency Stop Activated',
    emergencyStopDescription: 'All robot movements have been stopped for safety.',
    activeControl: 'Active Control',
    standby: 'Standby',
    startControl: 'Start Control',
    stopControl: 'Stop Control',
    controlMode: 'Control Mode',
    velocityControl: 'Velocity Control',
    positionControl: 'Position Control',
    torqueControl: 'Torque Control',
    velocityControlDesc: 'Control robot through velocity commands',
    positionControlDesc: 'Control robot through position targets',
    torqueControlDesc: 'Control robot through torque commands',
    // 状态信息
    physicsEngine: 'Physics Engine',
    renderer: 'Renderer',
    isaacGym: 'Isaac Gym',
    openGL: 'OpenGL',
    // 控制参数
    linearVelocity: 'Linear Velocity',
    angularVelocity: 'Angular Velocity',
    forwardBackward: 'Forward/Backward',
    leftRight: 'Left/Right',
    upDown: 'Up/Down',
    rollPitchYaw: 'Roll/Pitch/Yaw',
    // 机器人状态
    jointPositions: 'Joint Positions',
    jointVelocities: 'Joint Velocities',
    jointTorques: 'Joint Torques',
    basePosition: 'Base Position',
    baseOrientation: 'Base Orientation',
    contactForces: 'Contact Forces',
  },

  // 部署相关
  deployment: {
    deployModel: 'Deploy Model',
    modelList: 'Model List',
    deploymentStatus: 'Deployment Status',
    version: 'Version',
    environment: 'Environment',
    modelDeployment: 'Model Deployment',
    exportConfig: 'Export Configuration',
    selectModel: 'Select Model',
    exportFormat: 'Export Format',
    targetPlatform: 'Target Platform',
    optimizationLevel: 'Optimization Level',
    startExport: 'Start Export',
    performanceTesting: 'Performance Testing',
  },

  // 仪表盘
  dashboard: {
    systemOverview: 'System Overview',
    trainingSessions: 'Training Sessions',
    modelsDeployed: 'Models Deployed',
    activeSimulations: 'Active Simulations',
    systemUptime: 'System Uptime',
    performanceMetrics: 'Performance Metrics',
    recentActivities: 'Recent Activities',
  },
};

const resources = {
  en: {
    translation: enUS,
  },
  'zh-CN': {
    translation: zhCN,
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh-CN', // 默认使用中文
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false,
    },
    
    react: {
      // react i18next special options (optional)
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i'],
    },
  });

export default i18n;