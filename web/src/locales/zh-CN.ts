// 中文本地化配置
export const zhCN = {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    refresh: '刷新',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    // 操作按钮
    start: '开始',
    stop: '停止',
    pause: '暂停',
    resume: '恢复',
    reset: '重置',
    apply: '应用',
    submit: '提交',
    close: '关闭',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    finish: '完成',
    // 状态文本
    enabled: '已启用',
    disabled: '已禁用',
    active: '活跃',
    inactive: '非活跃',
    available: '可用',
    unavailable: '不可用',
    // 时间相关
    now: '现在',
    today: '今天',
    yesterday: '昨天',
    thisWeek: '本周',
    thisMonth: '本月',
    // 数据单位
    bytes: '字节',
    kb: 'KB',
    mb: 'MB',
    gb: 'GB',
    tb: 'TB',
    // 表格操作
    actions: '操作',
    view: '查看',
    download: '下载',
    export: '导出',
    import: '导入',
    // 表单验证
    required: '必填项',
    invalid: '无效',
    tooShort: '太短',
    tooLong: '太长',
  },

  // 菜单
  menu: {
    dashboard: '仪表盘',
    training: '模型训练',
    simulation: '仿真环境',
    deployment: '模型部署',
    monitoring: '系统监控',
    settings: '系统设置',
  },

  // 状态
  status: {
    online: '在线',
    offline: '离线',
    running: '运行中',
    stopped: '已停止',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    connecting: '连接中',
    connected: '已连接',
    disconnected: '未连接',
  },

  // 训练相关
  training: {
    startTraining: '开始训练',
    stopTraining: '停止训练',
    pauseTraining: '暂停训练',
    resumeTraining: '恢复训练',
    trainingProgress: '训练进度',
    trainingJobs: '训练任务',
    trainingConfig: '训练配置',
    modelName: '模型名称',
    episodes: '训练轮次',
    learningRate: '学习率',
    batchSize: '批次大小',
    trainingManagement: '训练管理',
    currentStatus: '当前状态',
    progress: '进度',
    currentIteration: '当前迭代',
    bestReward: '最佳奖励',
    taskName: '任务名称',
    numEnvironments: '环境数量',
    maxIterations: '最大迭代次数',
    // 训练配置
    trainingConfiguration: '训练配置',
    simple: '简单',
    advanced: '高级',
    pleaseSelectTask: '请选择任务！',
    pleaseInputNumEnvs: '请输入环境数量！',
    pleaseInputMaxIterations: '请输入最大迭代次数！',
    pleaseInputLearningRate: '请输入学习率！',
    // 任务选项
    anymalCRoughTerrain: 'Anymal-C 崎岖地形',
    anymalCFlat: 'Anymal-C 平地',
    anymalCStairs: 'Anymal-C 楼梯',
    anymalCExtreme: 'Anymal-C 极端地形',
    zqsa01RoughTerrain: 'ZQSA01 崎岖地形',
    // 高级设置
    advancedSettings: '高级设置',
    experimentName: '实验名称',
    runName: '运行名称',
    resumeFromCheckpoint: '从检查点恢复',
    checkpoint: '检查点',
    // 训练状态
    trainingActive: '训练中',
    trainingIdle: '空闲',
    trainingPaused: '已暂停',
    trainingCompleted: '已完成',
    trainingFailed: '失败',
    // 训练指标
    totalReward: '总奖励',
    episodeLength: '回合长度',
    successRate: '成功率',
    averageReward: '平均奖励',
    // 训练任务表格
    id: 'ID',
    startTime: '开始时间',
    duration: '持续时间',
    reward: '奖励',
    actions: '操作',
    view: '查看',
    stop: '停止',
    pause: '暂停',
    resume: '恢复',
    // 训练界面硬编码文本
    currentStatusLabel: '当前状态',
    progressLabel: '进度',
    currentIterationLabel: '当前迭代',
    bestRewardLabel: '最佳奖励',
    startTrainingBtn: '开始训练',
    resumeTrainingBtn: '恢复',
    pauseTrainingBtn: '暂停',
    stopTrainingBtn: '停止',
    trainingJobsTitle: '训练任务',
    experimentNameLabel: '实验名称',
    runNameLabel: '运行名称',
    resumeTrainingLabel: '恢复训练',
    checkpointLabel: '检查点',
    enterExperimentName: '输入实验名称',
    enterRunName: '输入运行名称',
  },

  // 系统监控
  monitoring: {
    systemMetrics: '系统指标',
    cpuUsage: 'CPU使用率',
    gpuUsage: 'GPU使用率',
    memoryUsage: '内存使用率',
    networkTraffic: '网络流量',
    diskUsage: '磁盘使用率',
    systemMonitoring: '系统监控与日志',
    systemOverview: '系统概览',
    serviceHealth: '服务健康状态',
    systemLogs: '系统日志',
    activeAlerts: '活跃告警',
    errorMonitoring: '错误监控',
    // 详细监控指标
    temperature: '温度',
    powerConsumption: '功耗',
    fanSpeed: '风扇转速',
    uptime: '运行时间',
    loadAverage: '负载平均值',
    processCount: '进程数量',
    // 网络监控
    bandwidth: '带宽',
    latency: '延迟',
    packetLoss: '丢包率',
    connections: '连接数',
    // 存储监控
    readSpeed: '读取速度',
    writeSpeed: '写入速度',
    iops: 'IOPS',
    freeSpace: '可用空间',
    // 日志级别
    debug: '调试',
    info: '信息',
    warning: '警告',
    error: '错误',
    critical: '严重',
    // 告警状态
    normal: '正常',
    alertWarning: '警告',
    alertCritical: '严重',
    resolved: '已解决',
    // 服务状态
    healthy: '健康',
    unhealthy: '不健康',
    degraded: '降级',
    unknown: '未知',
    // 错误监控相关
    errorMonitoringDashboard: '错误监控与健康仪表盘',
    realTimeErrorTracking: '实时错误跟踪、服务健康监控和自动恢复系统',
    overallHealth: '整体健康状态',
    servicesHealthy: '健康服务',
    totalErrors: '总错误数',
    recoveryRate: '恢复率',
    errorDistribution: '错误分布',
    serviceHealthStatus: '服务健康状态',
    restartService: '重启此服务？',
    restart: '重启',
    reset: '重置',
    service: '服务',
    status: '状态',
    errors: '错误',
    recoveryAttempts: '恢复尝试',
    lastCheck: '最后检查',
    allSeverities: '所有严重程度',
    allCategories: '所有类别',
    allServices: '所有服务',
    unresolved: '未解决',
    all: '全部',
    errorHistory: '错误历史',
    errorDetails: '错误详情',
    time: '时间',
    severity: '严重程度',
    category: '类别',
    message: '消息',
    details: '详情',
    errorId: '错误ID',
    timestamp: '时间戳',
    function: '函数',
    exceptionType: '异常类型',
    retryCount: '重试次数',
    traceback: '堆栈跟踪',
    errorTimeline: '错误时间线',
    errorsByCategory: '按类别分类的错误',
    errorsBySeverity: '按严重程度分类的错误',
  },

  // 仿真相关
  simulation: {
    startSimulation: '开始仿真',
    stopSimulation: '停止仿真',
    simulationConfig: '仿真配置',
    environment: '环境',
    robotModel: '机器人模型',
    physics: '物理引擎',
    simulationRobotControl: '仿真与机器人控制',
    robotControl: '机器人控制',
    robotState: '机器人状态',
    advancedConfig: '高级配置',
    terrainType: '地形类型',
    simulationStatus: '仿真状态',
    applyConfig: '应用配置',
    // 环境配置
    environmentConfiguration: '环境配置',
    flatGround: '平地',
    roughTerrain: '崎岖地形',
    stairs: '楼梯',
    slope: '斜坡',
    randomObstacles: '随机障碍物',
    terrainLength: '地形长度',
    terrainWidth: '地形宽度',
    gravity: '重力',
    timeStep: '时间步长',
    // 机器人控制
    robotControlPanel: '机器人控制面板',
    emergencyStop: '紧急停止',
    emergencyStopActivated: '紧急停止已激活',
    emergencyStopDescription: '为了安全，所有机器人运动已停止。',
    activeControl: '主动控制',
    standby: '待机',
    startControl: '开始控制',
    stopControl: '停止控制',
    controlMode: '控制模式',
    velocityControl: '速度控制',
    positionControl: '位置控制',
    torqueControl: '扭矩控制',
    velocityControlDesc: '通过速度命令控制机器人',
    positionControlDesc: '通过位置目标控制机器人',
    torqueControlDesc: '通过扭矩命令控制机器人',
    // 状态信息
    physicsEngine: '物理引擎',
    renderer: '渲染器',
    isaacGym: 'Isaac Gym',
    openGL: 'OpenGL',
    // 控制参数
    linearVelocity: '线性速度',
    angularVelocity: '角速度',
    forwardBackward: '前进/后退',
    leftRight: '左/右',
    upDown: '上/下',
    rollPitchYaw: '滚转/俯仰/偏航',
    // 机器人状态
    jointPositions: '关节位置',
    jointVelocities: '关节速度',
    jointTorques: '关节扭矩',
    basePosition: '基座位置',
    baseOrientation: '基座方向',
    contactForces: '接触力',
    // 仿真界面硬编码文本
    windSpeed: '风速',
    frictionCoefficient: '摩擦系数',
    steps: '步数',
    simTime: '仿真时间',
    reward: '奖励',
    success: '成功率',
  },

  // 部署相关
  deployment: {
    deployModel: '部署模型',
    modelList: '模型列表',
    deploymentStatus: '部署状态',
    version: '版本',
    environment: '环境',
    modelDeployment: '模型部署',
    exportConfig: '导出配置',
    selectModel: '选择模型',
    exportFormat: '导出格式',
    targetPlatform: '目标平台',
    optimizationLevel: '优化级别',
    startExport: '开始导出',
    performanceTesting: '性能测试',
    // 模型管理
    availableModels: '可用模型',
    modelSize: '模型大小',
    lastModified: '最后修改',
    accuracy: '准确率',
    deploy: '部署',
    undeploy: '取消部署',
    download: '下载',
    // 导出选项
    onnx: 'ONNX',
    tensorrt: 'TensorRT',
    pytorch: 'PyTorch',
    // 平台选项
    cpu: 'CPU',
    gpu: 'GPU',
    edge: '边缘设备',
    // 优化级别
    none: '无',
    basic: '基础',
    aggressive: '激进',
    // 部署状态
    deploying: '部署中',
    deployed: '已部署',
    deploymentFailed: '部署失败',
    notDeployed: '未部署',
    // 部署界面硬编码文本
    connected: '已连接',
    disconnected: '已断开',
    downloadExportedModel: '下载导出的模型',
    deployModelTooltip: '部署模型',
    viewValidationResults: '查看验证结果',
    viewLogs: '查看日志',
    cancelJob: '取消任务',
    deleteJob: '删除任务',
    jobDeleted: '任务已删除',
    pleaseSelectModel: '请选择模型！',
    selectTrainedModel: '选择训练好的模型',
    refreshModels: '刷新模型',
    modelInformation: '模型信息',
    format: '格式',
    size: '大小',
    created: '创建时间',
    validateModel: '验证模型',
    pleaseSelectExportFormat: '请选择导出格式！',
    pleaseSelectTargetPlatform: '请选择目标平台！',
    customExportPath: '自定义导出路径（可选）',
    leaveEmptyForAutoPath: '留空则自动生成路径',
    enableQuantization: '启用量化（实验性）',
    modelsDeployed: '已部署模型',
    totalSize: '总大小',
    successRate: '成功率',
    activeJobs: '活跃任务',
    modelPerformanceTesting: '模型性能测试',
    inferencePerformanceTest: '推理性能测试',
    selectModelForTesting: '选择要测试的模型',
    chooseModelToTest: '选择要测试的模型',
    testIterations: '测试迭代次数',
    batchSize: '批次大小',
    startPerformanceTest: '开始性能测试',
    performanceTestResults: '性能测试结果',
    avgInferenceTime: '平均推理时间',
    throughput: '吞吐量',
    memoryUsage: '内存使用',
    stdDeviation: '标准差',
    accuracyValidation: '精度验证',
    testDataset: '测试数据集',
    chooseTestDataset: '选择测试数据集',
    validationSet: '验证集',
    testSet: '测试集',
    customDataset: '自定义数据集',
    metrics: '指标',
    selectMetricsToCompute: '选择要计算的指标',
    averageReward: '平均奖励',
    actionStability: '动作稳定性',
    trajectoryTracking: '轨迹跟踪',
    numberOfEpisodes: '回合数',
    startAccuracyTest: '开始精度测试',
    accuracyTestResults: '精度测试结果',
    modelComparison: '模型对比',
    model: '模型',
    inferenceTime: '推理时间',
    memory: '内存',
    versionManagement: '版本管理',
    modelVersionManagement: '模型版本管理',
    versionHistory: '版本历史',
    current: '当前',
    performance: '性能',
    actions: '操作',
    rollback: '回滚',
    compare: '对比',
    delete: '删除',
    versionAnalytics: '版本分析',
    totalVersions: '总版本数',
    latestVersion: '最新版本',
    performanceGain: '性能提升',
    avgUpdateFreq: '平均更新频率',
    quickActions: '快速操作',
    createNewVersion: '创建新版本',
    compareVersions: '对比版本',
    performanceTrend: '性能趋势',
    performanceTrendChart: '性能趋势图表将在此显示',
    deploymentManagement: '部署管理',
    serviceDisconnected: '服务已断开',
    refresh: '刷新',
    refreshedDeploymentStatus: '已刷新部署状态',
    activeJobsTab: '活跃任务',
    jobHistory: '任务历史',
    modelVersions: '模型版本',
    noActiveDeploymentJobs: '没有活跃的部署任务',
    noDeploymentHistory: '没有部署历史',
    validate: '验证',
    export: '导出',
    deploymentLogs: '部署日志',
    modelValidationResults: '模型验证结果',
    close: '关闭',
    modelValid: '模型有效',
    validationFailed: '验证失败',
    inputShapes: '输入形状',
    validationErrors: '验证错误',
  },

  // 仪表盘
  dashboard: {
    systemOverview: '系统概览',
    trainingSessions: '训练会话',
    modelsDeployed: '已部署模型',
    activeSimulations: '活跃仿真',
    systemUptime: '系统运行时间',
    performanceMetrics: '性能指标',
    recentActivities: '最近活动',
    // 统计卡片
    totalTrainingSessions: '总训练会话',
    totalModelsDeployed: '总部署模型',
    totalActiveSimulations: '总活跃仿真',
    averageTrainingTime: '平均训练时间',
    // 活动类型
    trainingStarted: '训练开始',
    trainingCompleted: '训练完成',
    modelDeployed: '模型部署',
    simulationStarted: '仿真开始',
    systemAlert: '系统告警',
    // 时间单位
    seconds: '秒',
    minutes: '分钟',
    hours: '小时',
    days: '天',
    // 图表标题
    trainingProgressChart: '训练进度图表',
    systemResourceChart: '系统资源图表',
    performanceTrendChart: '性能趋势图表',
  },
};

export default zhCN;