import React, { useState, useEffect, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  Typography,
  Space,
  Table,
  Tag,
  Select,
  Input,
  Button,
  Alert,
  Badge,
  Statistic,
  Progress,
  Tabs,
  List,
  Modal,
  notification,
  Tooltip,
  Switch,
  DatePicker,
  Drawer,
  Timeline,
  Descriptions,
  Empty,
  Spin,
  Rate,
  Divider,
  Popconfirm,
  message
} from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  BugOutlined,
  ReloadOutlined,
  SettingOutlined,
  SearchOutlined,
  FilterOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ThunderboltOutlined,
  DashboardOutlined,
  ApiOutlined,
  HeartOutlined,
  AlertOutlined,
  RestOutlined,
  ToolOutlined,
  FireOutlined,
  SafetyOutlined,
  MonitorOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  ErrorInfo,
  ErrorSeverity,
  ErrorCategory,
  ServiceHealth,
  HealthStatus,
  SystemHealth,
  ErrorStatistics,
  ErrorNotification,
  NotificationType,
  NotificationPriority
} from '../../types/zenoh-types';

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Search } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface ErrorMonitoringProps {
  zenohClient?: any;
}

const ErrorMonitoringView: React.FC<ErrorMonitoringProps> = ({ zenohClient }) => {
  const { t } = useTranslation();

  // State management
  const [errors, setErrors] = useState<ErrorInfo[]>([]);
  const [serviceHealth, setServiceHealth] = useState<Record<string, ServiceHealth>>({});
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [errorStats, setErrorStats] = useState<ErrorStatistics | null>(null);
  const [notifications, setNotifications] = useState<ErrorNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedError, setSelectedError] = useState<ErrorInfo | null>(null);
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  
  // Filters
  const [severityFilter, setSeverityFilter] = useState<ErrorSeverity | 'all'>('all');
  const [categoryFilter, setCategoryFilter] = useState<ErrorCategory | 'all'>('all');
  const [serviceFilter, setServiceFilter] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [onlyUnresolved, setOnlyUnresolved] = useState(false);

  // Data fetching
  const fetchErrorData = useCallback(async () => {
    if (!zenohClient) return;
    
    setLoading(true);
    try {
      // Fetch error history
      const errorHistory = await zenohClient.get('system/error_history');
      if (errorHistory) {
        setErrors(errorHistory);
      }

      // Fetch service health
      const healthData = await zenohClient.get('system/service_health');
      if (healthData) {
        setServiceHealth(healthData);
      }

      // Fetch system health
      const systemHealthData = await zenohClient.get('system/health_summary');
      if (systemHealthData) {
        setSystemHealth(systemHealthData);
      }

      // Fetch error statistics
      const statsData = await zenohClient.get('system/error_statistics');
      if (statsData) {
        setErrorStats(statsData);
      }

      // Fetch active notifications
      const notificationsData = await zenohClient.get('system/notifications');
      if (notificationsData) {
        setNotifications(notificationsData);
      }
    } catch (error) {
      console.error('Failed to fetch error monitoring data:', error);
      message.error('Failed to load error monitoring data');
    } finally {
      setLoading(false);
    }
  }, [zenohClient]);

  // Real-time subscriptions
  useEffect(() => {
    if (!zenohClient) return;

    const subscriptions = [
      // Subscribe to new errors
      zenohClient.subscribe('system/errors', (error: ErrorInfo) => {
        setErrors(prev => [error, ...prev.slice(0, 99)]); // Keep last 100 errors
        
        // Show notification for high/critical errors
        if (error.severity === ErrorSeverity.HIGH || error.severity === ErrorSeverity.CRITICAL) {
          notification.error({
            message: `${error.severity.toUpperCase()} Error in ${error.context.service_name}`,
            description: error.message,
            duration: error.severity === ErrorSeverity.CRITICAL ? 0 : 10,
          });
        }
      }),

      // Subscribe to health updates
      zenohClient.subscribe('system/service_health_updates', (health: Record<string, ServiceHealth>) => {
        setServiceHealth(health);
      }),

      // Subscribe to system health updates
      zenohClient.subscribe('system/health_summary_updates', (health: SystemHealth) => {
        setSystemHealth(health);
      }),

      // Subscribe to notifications
      zenohClient.subscribe('system/notifications', (notification: ErrorNotification) => {
        setNotifications(prev => [notification, ...prev]);
        
        // Show UI notification
        const showNotification = notification.type === NotificationType.ERROR ?
          message.error :
          notification.type === NotificationType.WARNING ?
          message.warning :
          message.info;
        
        showNotification(notification.title);
      })
    ];

    // Initial data fetch
    fetchErrorData();

    // Cleanup subscriptions
    return () => {
      subscriptions.forEach(unsub => unsub?.());
    };
  }, [zenohClient, fetchErrorData]);

  // Filtered data
  const filteredErrors = errors.filter(error => {
    if (severityFilter !== 'all' && error.severity !== severityFilter) return false;
    if (categoryFilter !== 'all' && error.category !== categoryFilter) return false;
    if (serviceFilter !== 'all' && error.context.service_name !== serviceFilter) return false;
    if (onlyUnresolved && error.resolved) return false;
    if (timeRange) {
      const errorTime = dayjs(error.timestamp * 1000);
      if (errorTime.isBefore(timeRange[0]) || errorTime.isAfter(timeRange[1])) return false;
    }
    return true;
  });

  // Severity color mapping
  const getSeverityColor = (severity: ErrorSeverity): string => {
    switch (severity) {
      case ErrorSeverity.LOW: return '#52c41a';
      case ErrorSeverity.MEDIUM: return '#faad14';
      case ErrorSeverity.HIGH: return '#fa8c16';
      case ErrorSeverity.CRITICAL: return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  // Health status color mapping
  const getHealthStatusColor = (status: HealthStatus): string => {
    switch (status) {
      case HealthStatus.HEALTHY: return '#52c41a';
      case HealthStatus.DEGRADED: return '#faad14';
      case HealthStatus.UNHEALTHY: return '#fa8c16';
      case HealthStatus.CRITICAL: return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  // Service actions
  const handleServiceAction = async (serviceName: string, action: string) => {
    try {
      await zenohClient.publish(`service/${serviceName}/commands`, {
        command: action,
        timestamp: Date.now()
      });
      message.success(`${action} command sent to ${serviceName}`);
    } catch (error) {
      message.error(`Failed to send ${action} command to ${serviceName}`);
    }
  };

  // Error details modal
  const showErrorDetailsModal = (error: ErrorInfo) => {
    setSelectedError(error);
    setShowErrorDetails(true);
  };

  // Error statistics chart
  const getErrorStatsChartOption = () => {
    if (!errorStats) return {};

    return {
      title: {
        text: 'Error Distribution',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: 'Errors by Severity',
          type: 'pie',
          radius: '50%',
          data: Object.entries(errorStats.errors_by_severity).map(([severity, count]) => ({
            value: count,
            name: severity.charAt(0).toUpperCase() + severity.slice(1),
            itemStyle: {
              color: getSeverityColor(severity as ErrorSeverity)
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  // Error timeline chart
  const getErrorTimelineChartOption = () => {
    const timelineData = filteredErrors
      .slice(0, 50)
      .map(error => ({
        timestamp: error.timestamp * 1000,
        severity: error.severity,
        service: error.context.service_name,
        message: error.message
      }))
      .sort((a, b) => a.timestamp - b.timestamp);

    return {
      title: {
        text: 'Error Timeline',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0]?.data;
          if (data) {
            return `
              <strong>${dayjs(data[0]).format('YYYY-MM-DD HH:mm:ss')}</strong><br/>
              Service: ${data[2]}<br/>
              Severity: ${data[1]}<br/>
              Message: ${data[3]}
            `;
          }
          return '';
        }
      },
      xAxis: {
        type: 'time',
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'category',
        data: ['Low', 'Medium', 'High', 'Critical'],
        axisLabel: {
          formatter: (value: string) => value
        }
      },
      series: [
        {
          name: 'Errors',
          type: 'scatter',
          symbolSize: 8,
          data: timelineData.map(item => [
            item.timestamp,
            item.severity,
            item.service,
            item.message
          ]),
          itemStyle: {
            color: (params: any) => getSeverityColor(params.data[1])
          }
        }
      ]
    };
  };

  // Error table columns
  const errorTableColumns = [
    {
      title: t('monitoring.time'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
      render: (timestamp: number) => dayjs(timestamp * 1000).format('HH:mm:ss'),
      sorter: (a: ErrorInfo, b: ErrorInfo) => b.timestamp - a.timestamp,
      defaultSortOrder: 'descend' as const,
    },
    {
      title: t('monitoring.service'),
      dataIndex: ['context', 'service_name'],
      key: 'service',
      width: 120,
      filters: [...new Set(errors.map(e => e.context.service_name))].map(service => ({
        text: service,
        value: service,
      })),
      onFilter: (value: any, record: ErrorInfo) => record.context.service_name === value,
    },
    {
      title: t('monitoring.severity'),
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: ErrorSeverity) => (
        <Tag color={getSeverityColor(severity)}>
          {severity.toUpperCase()}
        </Tag>
      ),
      filters: Object.values(ErrorSeverity).map(severity => ({
        text: severity.toUpperCase(),
        value: severity,
      })),
      onFilter: (value: any, record: ErrorInfo) => record.severity === value,
    },
    {
      title: t('monitoring.category'),
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: ErrorCategory) => (
        <Tag>{category.replace('_', ' ').toUpperCase()}</Tag>
      ),
    },
    {
      title: t('monitoring.message'),
      dataIndex: 'message',
      key: 'message',
      ellipsis: { showTitle: false },
      render: (message: string) => (
        <Tooltip title={message}>
          <Text ellipsis style={{ maxWidth: 300 }}>
            {message}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: t('monitoring.status'),
      key: 'status',
      width: 100,
      render: (_: any, record: ErrorInfo) => (
        <Tag color={record.resolved ? 'green' : 'red'}>
          {record.resolved ? (
            <>
              <CheckCircleOutlined /> {t('monitoring.resolved')}
            </>
          ) : (
            <>
              <ExclamationCircleOutlined /> {t('monitoring.active')}
            </>
          )}
        </Tag>
      ),
    },
    {
      title: t('monitoring.actions'),
      key: 'actions',
      width: 100,
      render: (_: any, record: ErrorInfo) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={() => showErrorDetailsModal(record)}
          >
            {t('monitoring.details')}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <MonitorOutlined /> {t('monitoring.errorMonitoringDashboard')}
        </Title>
        <Text type="secondary">
          {t('monitoring.realTimeErrorTracking')}
        </Text>
      </div>

      {/* System Health Overview */}
      {systemHealth && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('monitoring.overallHealth')}
                value={systemHealth.overall_health.toUpperCase()}
                valueStyle={{
                  color: systemHealth.overall_health === 'healthy' ? '#3f8600' :
                         systemHealth.overall_health === 'degraded' ? '#cf1322' : '#722ed1'
                }}
                prefix={systemHealth.overall_health === 'healthy' ?
                       <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('monitoring.cpuUsage')}
                value={systemHealth.system_metrics.cpu_usage}
                suffix="%"
                precision={1}
                valueStyle={{
                  color: systemHealth.system_metrics.cpu_usage > 80 ? '#cf1322' : '#3f8600'
                }}
                prefix={<DashboardOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('monitoring.memoryUsage')}
                value={systemHealth.system_metrics.memory_usage}
                suffix="%"
                precision={1}
                valueStyle={{
                  color: systemHealth.system_metrics.memory_usage > 85 ? '#cf1322' : '#3f8600'
                }}
                prefix={<ApiOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('monitoring.servicesHealthy')}
                value={Object.values(systemHealth.services).filter(s => s.status === 'healthy').length}
                suffix={`/ ${Object.keys(systemHealth.services).length}`}
                valueStyle={{
                  color: Object.values(systemHealth.services).every(s => s.status === 'healthy') ?
                         '#3f8600' : '#cf1322'
                }}
                prefix={<HeartOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Error Statistics */}
      {errorStats && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic
                title={t('monitoring.totalErrors')}
                value={errorStats.total_errors}
                prefix={<BugOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title={t('monitoring.resolved')}
                value={errorStats.resolved_errors}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title={t('monitoring.recoveryRate')}
                value={errorStats.recovery_success_rate * 100}
                suffix="%"
                precision={1}
                prefix={<SyncOutlined />}
                valueStyle={{
                  color: errorStats.recovery_success_rate > 0.8 ? '#3f8600' :
                         errorStats.recovery_success_rate > 0.5 ? '#faad14' : '#cf1322'
                }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('monitoring.errorDistribution')} style={{ height: '200px' }}>
              <ReactECharts
                option={getErrorStatsChartOption()}
                style={{ height: '150px' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Service Health Grid */}
      {Object.keys(serviceHealth).length > 0 && (
        <Card title={t('monitoring.serviceHealthStatus')} style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            {Object.entries(serviceHealth).map(([serviceName, health]) => (
              <Col span={8} key={serviceName}>
                <Card
                  size="small"
                  style={{
                    borderColor: getHealthStatusColor(health.status),
                    borderWidth: '2px'
                  }}
                  actions={[
                    <Popconfirm
                      key="restart"
                      title={t('monitoring.restartService')}
                      onConfirm={() => handleServiceAction(serviceName, 'restart')}
                    >
                      <Button type="link" icon={<ReloadOutlined />}>
                        {t('monitoring.restart')}
                      </Button>
                    </Popconfirm>,
                    <Button
                      key="reset"
                      type="link"
                      icon={<SettingOutlined />}
                      onClick={() => handleServiceAction(serviceName, 'reset_state')}
                    >
                      {t('monitoring.reset')}
                    </Button>
                  ]}
                >
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label={t('monitoring.service')}>
                      <Text strong>{serviceName}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label={t('monitoring.status')}>
                      <Tag color={getHealthStatusColor(health.status)}>
                        {health.status.toUpperCase()}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label={t('monitoring.errors')}>
                      {health.error_count}
                    </Descriptions.Item>
                    <Descriptions.Item label={t('monitoring.recoveryAttempts')}>
                      {health.recovery_attempts}
                    </Descriptions.Item>
                    <Descriptions.Item label={t('monitoring.lastCheck')}>
                      {dayjs(health.last_check * 1000).fromNow()}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* Error Timeline and Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle" style={{ marginBottom: '16px' }}>
          <Col>
            <Space>
              <Select
                style={{ width: 120 }}
                value={severityFilter}
                onChange={setSeverityFilter}
                placeholder="Severity"
              >
                <Option value="all">{t('monitoring.allSeverities')}</Option>
                {Object.values(ErrorSeverity).map(severity => (
                  <Option key={severity} value={severity}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Option>
                ))}
              </Select>

              <Select
                style={{ width: 120 }}
                value={categoryFilter}
                onChange={setCategoryFilter}
                placeholder={t('monitoring.category')}
              >
                <Option value="all">{t('monitoring.allCategories')}</Option>
                {Object.values(ErrorCategory).map(category => (
                  <Option key={category} value={category}>
                    {category.replace('_', ' ').toUpperCase()}
                  </Option>
                ))}
              </Select>

              <Select
                style={{ width: 120 }}
                value={serviceFilter}
                onChange={setServiceFilter}
                placeholder={t('monitoring.service')}
              >
                <Option value="all">{t('monitoring.allServices')}</Option>
                {[...new Set(errors.map(e => e.context.service_name))].map(service => (
                  <Option key={service} value={service}>
                    {service}
                  </Option>
                ))}
              </Select>

              <RangePicker
                showTime
                value={timeRange}
                onChange={(dates) => setTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                style={{ width: 300 }}
              />

              <Switch
                checked={onlyUnresolved}
                onChange={setOnlyUnresolved}
                checkedChildren={t('monitoring.unresolved')}
                unCheckedChildren={t('monitoring.all')}
              />

              <Button
                icon={<ReloadOutlined />}
                onClick={fetchErrorData}
                loading={loading}
              >
                {t('common.refresh')}
              </Button>
            </Space>
          </Col>
        </Row>

        <ReactECharts
          option={getErrorTimelineChartOption()}
          style={{ height: '300px' }}
        />
      </Card>

      {/* Error Table */}
      <Card title={`${t('monitoring.errorHistory')} (${filteredErrors.length} ${t('monitoring.errors')})`}>
        <Table
          columns={errorTableColumns}
          dataSource={filteredErrors}
          rowKey="error_id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} errors`
          }}
          scroll={{ x: 1200 }}
          loading={loading}
          size="small"
        />
      </Card>

      {/* Error Details Modal */}
      <Modal
        title={t('monitoring.errorDetails')}
        visible={showErrorDetails}
        onCancel={() => setShowErrorDetails(false)}
        footer={[
          <Button key="close" onClick={() => setShowErrorDetails(false)}>
            {t('monitoring.close')}
          </Button>
        ]}
        width={800}
      >
        {selectedError && (
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label={t('monitoring.errorId')}>
              <Text code>{selectedError.error_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.timestamp')}>
              {dayjs(selectedError.timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.service')}>
              <Tag>{selectedError.context.service_name}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.function')}>
              <Text code>{selectedError.context.function_name}</Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.severity')}>
              <Tag color={getSeverityColor(selectedError.severity)}>
                {selectedError.severity.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.category')}>
              <Tag>{selectedError.category.replace('_', ' ').toUpperCase()}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.exceptionType')}>
              <Text code>{selectedError.exception_type}</Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.message')}>
              {selectedError.message}
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.retryCount')}>
              {selectedError.retry_count || 0}
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.status')}>
              <Tag color={selectedError.resolved ? 'green' : 'red'}>
                {selectedError.resolved ? t('monitoring.resolved') : t('monitoring.active')}
                {selectedError.resolved && selectedError.resolution_method && (
                  <Text type="secondary"> ({selectedError.resolution_method})</Text>
                )}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('monitoring.traceback')}>
              <Text code>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                  {selectedError.traceback}
                </pre>
              </Text>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ErrorMonitoringView;