import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ResourceMonitor from '../ResourceMonitor';

// Mock ECharts component
jest.mock('echarts-for-react', () => {
  return jest.fn(() => <div data-testid="echarts-mock" />);
});

describe('ResourceMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders without crashing', async () => {
    await act(async () => {
      render(<ResourceMonitor />);
    });
    
    expect(screen.getByText('System Resource Monitor')).toBeInTheDocument();
  });

  it('displays all resource cards', async () => {
    await act(async () => {
      render(<ResourceMonitor />);
    });
    
    expect(screen.getByText('CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Memory Usage')).toBeInTheDocument();
    expect(screen.getByText('Disk Usage')).toBeInTheDocument();
    expect(screen.getByText('Network I/O')).toBeInTheDocument();
    expect(screen.getByText('GPU Usage')).toBeInTheDocument();
  });

  it('displays resource usage trends chart', async () => {
    await act(async () => {
      render(<ResourceMonitor />);
    });
    
    expect(screen.getByText('Resource Usage Trends')).toBeInTheDocument();
    expect(screen.getByText('Real-time monitoring')).toBeInTheDocument();
  });

  it('shows process information', async () => {
    await act(async () => {
      render(<ResourceMonitor />);
    });
    
    expect(screen.getByText('Top Processes')).toBeInTheDocument();
  });

  it('renders charts', async () => {
    await act(async () => {
      render(<ResourceMonitor />);
    });
    
    const charts = screen.getAllByTestId('echarts-mock');
    expect(charts.length).toBeGreaterThan(0);
  });
});