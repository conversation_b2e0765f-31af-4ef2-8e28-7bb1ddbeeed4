import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

// Simple test to verify test setup works
describe('Test Setup Verification', () => {
  it('should be able to render a simple React component', () => {
    const TestComponent = () => <div>Hello Test</div>;
    
    const { getByText } = render(<TestComponent />);
    
    expect(getByText('Hello Test')).toBeInTheDocument();
  });
});