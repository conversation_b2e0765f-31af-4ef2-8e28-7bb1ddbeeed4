// Mock Zenoh Client interface for monitoring tests
export interface MockZenohClient {
  isConnected: jest.MockedFunction<() => boolean>;
  sendCommand: jest.MockedFunction<(topic: string, command: string, parameters?: any) => void>;
  subscribe: jest.MockedFunction<(topic: string, callback: (message: any) => void) => string>;
  unsubscribe: jest.MockedFunction<(subscriptionId: string) => void>;
}

export const createMockZenohClient = (connected = true): MockZenohClient => ({
  isConnected: jest.fn().mockReturnValue(connected) as jest.MockedFunction<() => boolean>,
  sendCommand: jest.fn() as jest.MockedFunction<(topic: string, command: string, parameters?: any) => void>,
  subscribe: jest.fn().mockReturnValue('mock-subscription-id') as jest.MockedFunction<(topic: string, callback: (message: any) => void) => string>,
  unsubscribe: jest.fn() as jest.MockedFunction<(subscriptionId: string) => void>,
});

// Mock system metrics data
export const mockSystemMetrics = {
  timestamp: '14:30:00',
  cpu: 45.2,
  memory: 62.1,
  gpu: 23.5,
  network: 12.8,
  disk: 73.4,
  temperature: 58.0
};

// Mock service health data
export const mockServiceHealth = [
  {
    name: 'Training Service',
    status: 'healthy' as const,
    uptime: '72h 15m',
    lastHeartbeat: '2s ago',
    version: '2.1.3',
    metrics: {
      responseTime: 45,
      errorRate: 0.1,
      throughput: 1250,
      activeConnections: 8
    }
  },
  {
    name: 'Simulation Service',
    status: 'healthy' as const,
    uptime: '71h 42m',
    lastHeartbeat: '1s ago',
    version: '2.1.2',
    metrics: {
      responseTime: 23,
      errorRate: 0.05,
      throughput: 2100,
      activeConnections: 12
    }
  },
  {
    name: 'Play Service',
    status: 'critical' as const,
    uptime: '2h 08m',
    lastHeartbeat: '45s ago',
    version: '2.1.0',
    metrics: {
      responseTime: 890,
      errorRate: 12.4,
      throughput: 12,
      activeConnections: 1
    }
  }
];

// Mock log entries
export const mockLogEntries = [
  {
    key: '1',
    timestamp: '2024-01-15T14:30:00Z',
    level: 'info' as const,
    service: 'training_service',
    message: 'Training session started successfully',
    correlationId: 'train-001',
    userId: 'user_123'
  },
  {
    key: '2',
    timestamp: '2024-01-15T14:28:00Z',
    level: 'warning' as const,
    service: 'simulation_service',
    message: 'High memory usage detected',
    correlationId: 'sim-002',
    userId: 'user_123'
  },
  {
    key: '3',
    timestamp: '2024-01-15T14:25:00Z',
    level: 'error' as const,
    service: 'deployment_service',
    message: 'Failed to export model to ONNX format',
    correlationId: 'deploy-003',
    userId: 'user_456',
    details: {
      model_path: '/models/test.pt',
      error_code: 'EXPORT_FAILED'
    }
  },
  {
    key: '4',
    timestamp: '2024-01-15T14:20:00Z',
    level: 'critical' as const,
    service: 'play_service',
    message: 'Service crashed due to out of memory error',
    correlationId: 'play-004',
    userId: 'system'
  }
];

// Mock system alerts
export const mockSystemAlerts = [
  {
    id: 'alert-001',
    title: 'High Memory Usage',
    description: 'System memory usage exceeded 85% threshold',
    severity: 'warning' as const,
    timestamp: '2024-01-15T14:25:00Z',
    service: 'system',
    acknowledged: false
  },
  {
    id: 'alert-002',
    title: 'Service Response Time',
    description: 'Play Service response time increased significantly',
    severity: 'critical' as const,
    timestamp: '2024-01-15T14:20:00Z',
    service: 'play_service',
    acknowledged: false
  },
  {
    id: 'alert-003',
    title: 'GPU Temperature Warning',
    description: 'GPU temperature reached 85°C',
    severity: 'warning' as const,
    timestamp: '2024-01-15T14:15:00Z',
    service: 'system',
    acknowledged: true
  }
];

// Mock alert rules
export const mockAlertRules = [
  {
    id: 'rule-001',
    name: 'High CPU Usage',
    type: 'metric' as const,
    condition: 'cpu > threshold',
    threshold: 80,
    severity: 'warning' as const,
    enabled: true,
    notifications: ['email', 'slack']
  },
  {
    id: 'rule-002',
    name: 'Memory Critical',
    type: 'metric' as const,
    condition: 'memory > threshold',
    threshold: 90,
    severity: 'critical' as const,
    enabled: true,
    notifications: ['email', 'sms', 'slack']
  },
  {
    id: 'rule-003',
    name: 'Service Error Rate',
    type: 'metric' as const,
    condition: 'error_rate > threshold',
    threshold: 5,
    severity: 'warning' as const,
    enabled: false,
    notifications: ['slack']
  }
];

// Mock resource data for ResourceMonitor
export const mockResourceData = {
  cpu: {
    name: 'CPU Usage',
    current: 45.2,
    average: 38.7,
    peak: 87.3,
    threshold: 80,
    unit: '%',
    status: 'normal' as const,
    trend: 'stable' as const,
    details: {
      processes: 156,
      temperature: 58
    }
  },
  memory: {
    name: 'Memory Usage',
    current: 62.1,
    average: 58.3,
    peak: 89.2,
    threshold: 85,
    unit: '%',
    status: 'normal' as const,
    trend: 'up' as const,
    details: {
      total: 16384,
      used: 10178,
      available: 6206
    }
  },
  gpu: {
    name: 'GPU Usage',
    current: 23.5,
    average: 31.2,
    peak: 95.1,
    threshold: 90,
    unit: '%',
    status: 'normal' as const,
    trend: 'stable' as const,
    details: {
      temperature: 52,
      total: 8192,
      used: 1925
    }
  }
};

// Mock process information
export const mockProcessData = [
  {
    pid: 1234,
    name: 'training_service',
    cpu: 15.2,
    memory: 2048,
    user: 'admin'
  },
  {
    pid: 5678,
    name: 'simulation_service',
    cpu: 12.8,
    memory: 1536,
    user: 'admin'
  },
  {
    pid: 9012,
    name: 'python',
    cpu: 8.5,
    memory: 1024,
    user: 'admin'
  },
  {
    pid: 3456,
    name: 'zenoh_router',
    cpu: 3.2,
    memory: 256,
    user: 'admin'
  }
];

// Mock Zenoh message payloads
export const mockZenohMessages = {
  metricsUpdate: {
    payload: mockSystemMetrics
  },
  healthUpdate: {
    payload: {
      service_name: 'training_service',
      status: 'healthy',
      uptime: '72h 16m',
      lastHeartbeat: '1s ago',
      metrics: {
        responseTime: 43,
        errorRate: 0.09,
        throughput: 1260,
        activeConnections: 9
      }
    }
  },
  logUpdate: {
    payload: {
      timestamp: '2024-01-15T14:35:00Z',
      level: 'info',
      service: 'config_service',
      message: 'Configuration updated successfully',
      correlation_id: 'config-005',
      user_id: 'user_789'
    }
  },
  alertUpdate: {
    payload: {
      id: 'alert-new-001',
      title: 'Disk Space Warning',
      description: 'Available disk space is below 10%',
      severity: 'warning',
      timestamp: '2024-01-15T14:35:00Z',
      service: 'system',
      acknowledged: false
    }
  }
};

// Test utilities
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const simulateRealtimeUpdate = async (callback: Function, data: any, delay = 100) => {
  await waitForNextTick();
  callback(data);
  await new Promise(resolve => setTimeout(resolve, delay));
};

export const createMockEChartsOption = () => ({
  grid: { top: 10, left: 10, right: 10, bottom: 10 },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: { show: false }
  },
  yAxis: {
    type: 'value',
    axisLabel: { show: false }
  },
  series: [{
    type: 'line',
    data: [10, 20, 30, 40, 50],
    smooth: true
  }]
});

// Helper functions for test assertions
export const expectElementToBeVisible = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument();
  expect(element).toBeVisible();
};

export const expectElementToHaveText = (element: HTMLElement | null, text: string) => {
  expect(element).toBeInTheDocument();
  expect(element).toHaveTextContent(text);
};

export const expectChartToBeRendered = (container: HTMLElement, chartCount = 1) => {
  const charts = container.querySelectorAll('[data-testid="echarts-mock"]');
  expect(charts).toHaveLength(chartCount);
};

export const expectProgressBarToShow = (container: HTMLElement, value: number) => {
  const progressBars = container.querySelectorAll('.ant-progress-text');
  const found = Array.from(progressBars).some(bar => 
    bar.textContent?.includes(`${value}%`)
  );
  expect(found).toBe(true);
};

export const expectAlertToBeDisplayed = (container: HTMLElement, alertTitle: string) => {
  expect(container).toHaveTextContent(alertTitle);
};

export const expectLogEntryToBeVisible = (container: HTMLElement, message: string) => {
  expect(container).toHaveTextContent(message);
};

export default {
  createMockZenohClient,
  mockSystemMetrics,
  mockServiceHealth,
  mockLogEntries,
  mockSystemAlerts,
  mockAlertRules,
  mockResourceData,
  mockProcessData,
  mockZenohMessages,
  waitForNextTick,
  simulateRealtimeUpdate,
  createMockEChartsOption,
  expectElementToBeVisible,
  expectElementToHaveText,
  expectChartToBeRendered,
  expectProgressBarToShow,
  expectAlertToBeDisplayed,
  expectLogEntryToBeVisible
};