import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Card, Typography, Space, Table, Tag, Select, Input, Button, Alert, Badge, Statistic, Progress, Tabs, List, Modal, notification, Tooltip, Switch, DatePicker, Drawer } from 'antd';
import {
  MonitorOutlined,
  SearchOutlined,
  ReloadOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
  BellOutlined,
  SettingOutlined,
  DownloadOutlined,
  FilterOutlined,
  BarChartOutlined,
  DashboardOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import ErrorMonitoringView from './ErrorMonitoringView';

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Search } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface MonitoringViewProps {
  zenohClient?: any;
}

interface LogEntry {
  key: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug' | 'critical';
  service: string;
  message: string;
  details?: any;
  correlationId?: string;
  userId?: string;
}

interface SystemMetric {
  timestamp: string;
  cpu: number;
  memory: number;
  gpu: number;
  network: number;
  disk: number;
  temperature: number;
}

interface ServiceHealth {
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  uptime: string;
  lastHeartbeat: string;
  version: string;
  metrics: {
    responseTime: number;
    errorRate: number;
    throughput: number;
    activeConnections: number;
  };
}

interface AlertRule {
  id: string;
  name: string;
  type: 'metric' | 'log' | 'service';
  condition: string;
  threshold: number;
  severity: 'info' | 'warning' | 'critical';
  enabled: boolean;
  notifications: string[];
}

interface SystemAlert {
  id: string;
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'critical';
  timestamp: string;
  service: string;
  acknowledged: boolean;
  resolvedAt?: string;
}

const MonitoringView: React.FC<MonitoringViewProps> = ({ zenohClient }) => {
  const { t } = useTranslation();
  // State management
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [metrics, setMetrics] = useState<SystemMetric[]>([]);
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  
  // Filter states
  const [logLevel, setLogLevel] = useState<string>('all');
  const [selectedService, setSelectedService] = useState<string>('all');
  const [logSearchTerm, setLogSearchTerm] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  
  // UI states
  const [activeTab, setActiveTab] = useState('overview');
  const [isAlertsModalVisible, setIsAlertsModalVisible] = useState(false);
  const [isSettingsDrawerVisible, setIsSettingsDrawerVisible] = useState(false);
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const [alertsEnabled, setAlertsEnabled] = useState(true);
  
  // System health state
  const [systemStatus, setSystemStatus] = useState({
    overall: 'healthy' as 'healthy' | 'warning' | 'critical',
    uptime: '72h 15m',
    totalServices: 5,
    healthyServices: 4,
    activeAlerts: 2,
    avgResponseTime: 125
  });

  // Load data on component mount
  useEffect(() => {
    initializeMonitoring();
    if (zenohClient) {
      setupZenohSubscriptions();
    }
  }, [zenohClient]);

  const initializeMonitoring = useCallback(() => {
    // Initialize with mock data
    generateInitialData();
    
    // Set up real-time updates if enabled
    if (realTimeEnabled) {
      const metricsInterval = setInterval(updateMetrics, 5000);
      const logsInterval = setInterval(addNewLog, 3000);
      const healthInterval = setInterval(updateServiceHealth, 10000);
      
      return () => {
        clearInterval(metricsInterval);
        clearInterval(logsInterval);
        clearInterval(healthInterval);
      };
    }
  }, [realTimeEnabled]);

  const setupZenohSubscriptions = useCallback(() => {
    if (!zenohClient) return;

    // Subscribe to system metrics
    const metricsSubscription = zenohClient.subscribe(
      'legged_gym/system/metrics',
      handleMetricsUpdate
    );

    // Subscribe to service health updates
    const healthSubscription = zenohClient.subscribe(
      'legged_gym/system/health',
      handleHealthUpdate
    );

    // Subscribe to system logs
    const logsSubscription = zenohClient.subscribe(
      'legged_gym/system/logs',
      handleLogUpdate
    );

    // Subscribe to alerts
    const alertsSubscription = zenohClient.subscribe(
      'legged_gym/system/alerts',
      handleAlertUpdate
    );

    // Cleanup on unmount
    return () => {
      zenohClient.unsubscribe(metricsSubscription);
      zenohClient.unsubscribe(healthSubscription);
      zenohClient.unsubscribe(logsSubscription);
      zenohClient.unsubscribe(alertsSubscription);
    };
  }, [zenohClient]);

  const handleMetricsUpdate = useCallback((message: any) => {
    try {
      const metricsData = message.payload;
      const newMetric: SystemMetric = {
        timestamp: new Date().toLocaleTimeString(),
        cpu: metricsData.cpu || 0,
        memory: metricsData.memory || 0,
        gpu: metricsData.gpu || 0,
        network: metricsData.network || 0,
        disk: metricsData.disk || 0,
        temperature: metricsData.temperature || 0
      };
      
      setMetrics(prev => [...prev.slice(-29), newMetric]);
      
      // Check for threshold violations
      checkMetricThresholds(newMetric);
    } catch (error) {
      console.error('Error handling metrics update:', error);
    }
  }, []);

  const handleHealthUpdate = useCallback((message: any) => {
    try {
      const healthData = message.payload;
      setServiceHealth(prev => prev.map(service => 
        service.name === healthData.service_name 
          ? { ...service, ...healthData }
          : service
      ));
    } catch (error) {
      console.error('Error handling health update:', error);
    }
  }, []);

  const handleLogUpdate = useCallback((message: any) => {
    try {
      const logData = message.payload;
      const newLog: LogEntry = {
        key: `${Date.now()}_${Math.random()}`,
        timestamp: logData.timestamp || new Date().toISOString(),
        level: logData.level || 'info',
        service: logData.service || 'unknown',
        message: logData.message || '',
        details: logData.details,
        correlationId: logData.correlation_id,
        userId: logData.user_id
      };
      
      setLogs(prev => [newLog, ...prev.slice(0, 999)]);
      
      // Check for log-based alerts
      checkLogAlerts(newLog);
    } catch (error) {
      console.error('Error handling log update:', error);
    }
  }, []);

  const handleAlertUpdate = useCallback((message: any) => {
    try {
      const alertData = message.payload;
      const newAlert: SystemAlert = {
        id: alertData.id || `alert_${Date.now()}`,
        title: alertData.title || 'System Alert',
        description: alertData.description || '',
        severity: alertData.severity || 'warning',
        timestamp: alertData.timestamp || new Date().toISOString(),
        service: alertData.service || 'system',
        acknowledged: alertData.acknowledged || false,
        resolvedAt: alertData.resolved_at
      };
      
      setSystemAlerts(prev => [newAlert, ...prev]);
      
      // Show notification if alerts are enabled
      if (alertsEnabled) {
        showAlertNotification(newAlert);
      }
    } catch (error) {
      console.error('Error handling alert update:', error);
    }
  }, [alertsEnabled]);

  const generateInitialData = () => {
    // Generate initial metrics
    const initialMetrics: SystemMetric[] = [];
    const now = Date.now();
    
    for (let i = 30; i >= 0; i--) {
      initialMetrics.push({
        timestamp: new Date(now - i * 60000).toLocaleTimeString(),
        cpu: 20 + Math.random() * 60,
        memory: 30 + Math.random() * 50,
        gpu: 10 + Math.random() * 80,
        network: Math.random() * 100,
        disk: 40 + Math.random() * 30,
        temperature: 35 + Math.random() * 20
      });
    }
    setMetrics(initialMetrics);

    // Generate initial service health data
    const services: ServiceHealth[] = [
      {
        name: 'Training Service',
        status: 'healthy',
        uptime: '72h 15m',
        lastHeartbeat: '2s ago',
        version: '2.1.3',
        metrics: {
          responseTime: 45,
          errorRate: 0.1,
          throughput: 1250,
          activeConnections: 8
        }
      },
      {
        name: 'Simulation Service',
        status: 'healthy',
        uptime: '71h 42m',
        lastHeartbeat: '1s ago',
        version: '2.1.2',
        metrics: {
          responseTime: 23,
          errorRate: 0.05,
          throughput: 2100,
          activeConnections: 12
        }
      },
      {
        name: 'Deployment Service',
        status: 'warning',
        uptime: '15h 23m',
        lastHeartbeat: '5s ago',
        version: '2.1.3',
        metrics: {
          responseTime: 156,
          errorRate: 2.3,
          throughput: 340,
          activeConnections: 3
        }
      },
      {
        name: 'Config Service',
        status: 'healthy',
        uptime: '72h 15m',
        lastHeartbeat: '1s ago',
        version: '2.1.1',
        metrics: {
          responseTime: 12,
          errorRate: 0.01,
          throughput: 45,
          activeConnections: 15
        }
      },
      {
        name: 'Play Service',
        status: 'critical',
        uptime: '2h 08m',
        lastHeartbeat: '45s ago',
        version: '2.1.0',
        metrics: {
          responseTime: 890,
          errorRate: 12.4,
          throughput: 12,
          activeConnections: 1
        }
      }
    ];
    setServiceHealth(services);

    // Generate initial logs
    generateInitialLogs();
    
    // Generate initial alerts
    const alerts: SystemAlert[] = [
      {
        id: 'alert_001',
        title: 'High Memory Usage',
        description: 'System memory usage exceeded 85% threshold',
        severity: 'warning',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        service: 'system',
        acknowledged: false
      },
      {
        id: 'alert_002',
        title: 'Service Response Time',
        description: 'Play Service response time increased significantly',
        severity: 'critical',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        service: 'play_service',
        acknowledged: false
      }
    ];
    setSystemAlerts(alerts);

    // Generate initial alert rules
    const rules: AlertRule[] = [
      {
        id: 'rule_001',
        name: 'High CPU Usage',
        type: 'metric',
        condition: 'cpu > threshold',
        threshold: 80,
        severity: 'warning',
        enabled: true,
        notifications: ['email', 'slack']
      },
      {
        id: 'rule_002',
        name: 'Memory Critical',
        type: 'metric',
        condition: 'memory > threshold',
        threshold: 90,
        severity: 'critical',
        enabled: true,
        notifications: ['email', 'sms', 'slack']
      },
      {
        id: 'rule_003',
        name: 'Service Error Rate',
        type: 'metric',
        condition: 'error_rate > threshold',
        threshold: 5,
        severity: 'warning',
        enabled: true,
        notifications: ['slack']
      }
    ];
    setAlertRules(rules);
  };

  const generateInitialLogs = () => {
    const levels: Array<'info' | 'warning' | 'error' | 'debug' | 'critical'> = 
      ['info', 'warning', 'error', 'debug', 'critical'];
    const services = ['training_service', 'simulation_service', 'deployment_service', 'config_service', 'play_service'];
    const messages = [
      'Service started successfully',
      'Configuration updated',
      'Model checkpoint saved',
      'Training iteration completed',
      'Connection established',
      'Warning: High memory usage detected',
      'Error: Failed to connect to Zenoh router',
      'Debug: Processing training batch',
      'Simulation environment initialized',
      'Model exported successfully',
      'Critical: Service unresponsive',
      'User session started',
      'Database connection restored',
      'Cache cleared successfully',
      'Backup process completed'
    ];

    const newLogs: LogEntry[] = [];
    for (let i = 0; i < 100; i++) {
      newLogs.push({
        key: i.toString(),
        timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        level: levels[Math.floor(Math.random() * levels.length)],
        service: services[Math.floor(Math.random() * services.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        correlationId: Math.random().toString(36).substring(7),
        userId: `user_${Math.floor(Math.random() * 100)}`
      });
    }
    
    newLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    setLogs(newLogs);
    setFilteredLogs(newLogs);
  };

  const updateMetrics = () => {
    const newMetric: SystemMetric = {
      timestamp: new Date().toLocaleTimeString(),
      cpu: 20 + Math.random() * 60,
      memory: 30 + Math.random() * 50,
      gpu: 10 + Math.random() * 80,
      network: Math.random() * 100,
      disk: 40 + Math.random() * 30,
      temperature: 35 + Math.random() * 20
    };
    
    setMetrics(prev => [...prev.slice(-29), newMetric]);
    checkMetricThresholds(newMetric);
  };

  const addNewLog = () => {
    const levels: Array<'info' | 'warning' | 'error' | 'debug' | 'critical'> = 
      ['info', 'warning', 'error', 'debug', 'critical'];
    const services = ['training_service', 'simulation_service', 'deployment_service', 'config_service', 'play_service'];
    const messages = [
      'Service heartbeat',
      'Configuration synchronized',
      'Training metrics updated',
      'Model validation completed',
      'System status check',
      'Connection pool refreshed',
      'Cache hit ratio optimized',
      'Resource cleanup completed'
    ];

    const newLog: LogEntry = {
      key: `${Date.now()}_${Math.random()}`,
      timestamp: new Date().toISOString(),
      level: levels[Math.floor(Math.random() * levels.length)],
      service: services[Math.floor(Math.random() * services.length)],
      message: messages[Math.floor(Math.random() * messages.length)],
      correlationId: Math.random().toString(36).substring(7),
      userId: `user_${Math.floor(Math.random() * 100)}`
    };

    setLogs(prev => [newLog, ...prev.slice(0, 999)]);
    checkLogAlerts(newLog);
  };

  const updateServiceHealth = () => {
    setServiceHealth(prev => prev.map(service => ({
      ...service,
      lastHeartbeat: `${Math.floor(Math.random() * 10)}s ago`,
      metrics: {
        ...service.metrics,
        responseTime: service.metrics.responseTime + (Math.random() - 0.5) * 20,
        errorRate: Math.max(0, service.metrics.errorRate + (Math.random() - 0.5) * 1),
        throughput: service.metrics.throughput + (Math.random() - 0.5) * 100,
        activeConnections: Math.max(0, service.metrics.activeConnections + Math.floor((Math.random() - 0.5) * 4))
      }
    })));
  };

  const checkMetricThresholds = (metric: SystemMetric) => {
    alertRules.filter(rule => rule.enabled && rule.type === 'metric').forEach(rule => {
      let value = 0;
      let metricName = '';
      
      if (rule.condition.includes('cpu')) {
        value = metric.cpu;
        metricName = 'CPU';
      } else if (rule.condition.includes('memory')) {
        value = metric.memory;
        metricName = 'Memory';
      } else if (rule.condition.includes('gpu')) {
        value = metric.gpu;
        metricName = 'GPU';
      }
      
      if (value > rule.threshold) {
        const alert: SystemAlert = {
          id: `threshold_${Date.now()}`,
          title: `${metricName} Threshold Exceeded`,
          description: `${metricName} usage is ${value.toFixed(1)}%, exceeding threshold of ${rule.threshold}%`,
          severity: rule.severity,
          timestamp: new Date().toISOString(),
          service: 'system',
          acknowledged: false
        };
        
        setSystemAlerts(prev => [alert, ...prev]);
        
        if (alertsEnabled) {
          showAlertNotification(alert);
        }
      }
    });
  };

  const checkLogAlerts = (log: LogEntry) => {
    if (log.level === 'error' || log.level === 'critical') {
      const alert: SystemAlert = {
        id: `log_${Date.now()}`,
        title: `${log.level.toUpperCase()} Log Entry`,
        description: log.message,
        severity: log.level === 'critical' ? 'critical' : 'warning',
        timestamp: log.timestamp,
        service: log.service,
        acknowledged: false
      };
      
      setSystemAlerts(prev => [alert, ...prev]);
      
      if (alertsEnabled) {
        showAlertNotification(alert);
      }
    }
  };

  const showAlertNotification = (alert: SystemAlert) => {
    const notificationType = alert.severity === 'critical' ? 'error' : 
                            alert.severity === 'warning' ? 'warning' : 'info';
    
    notification[notificationType]({
      message: alert.title,
      description: alert.description,
      placement: 'topRight',
      duration: alert.severity === 'critical' ? 0 : 4.5,
      key: alert.id
    });
  };

  // Filter logs based on current filters
  useEffect(() => {
    let filtered = logs;
    
    if (logLevel !== 'all') {
      filtered = filtered.filter(log => log.level === logLevel);
    }
    
    if (selectedService !== 'all') {
      filtered = filtered.filter(log => log.service === selectedService);
    }
    
    if (logSearchTerm) {
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(logSearchTerm.toLowerCase()) ||
        log.service.toLowerCase().includes(logSearchTerm.toLowerCase())
      );
    }
    
    if (dateRange && dateRange.length === 2) {
      filtered = filtered.filter(log => {
        const logTime = dayjs(log.timestamp);
        return logTime.isAfter(dateRange[0]) && logTime.isBefore(dateRange[1]);
      });
    }
    
    setFilteredLogs(filtered);
  }, [logs, logLevel, selectedService, logSearchTerm, dateRange]);

  const acknowledgeAlert = (alertId: string) => {
    setSystemAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
    // Close notification using destroy method
    notification.destroy(alertId);
  };

  const resolveAlert = (alertId: string) => {
    setSystemAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolvedAt: new Date().toISOString() } : alert
    ));
  };

  const exportLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `system_logs_${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const logColumns = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a: LogEntry, b: LogEntry) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    },
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: string) => {
        const colors = {
          info: 'blue',
          warning: 'orange',
          error: 'red',
          debug: 'gray',
          critical: 'purple',
        };
        const icons = {
          info: <InfoCircleOutlined />,
          warning: <WarningOutlined />,
          error: <StopOutlined />,
          debug: <SearchOutlined />,
          critical: <ExclamationCircleOutlined />,
        };
        return (
          <Tag color={colors[level as keyof typeof colors]} icon={icons[level as keyof typeof icons]}>
            {level.toUpperCase()}
          </Tag>
        );
      },
      filters: [
        { text: 'Info', value: 'info' },
        { text: 'Warning', value: 'warning' },
        { text: 'Error', value: 'error' },
        { text: 'Debug', value: 'debug' },
        { text: 'Critical', value: 'critical' },
      ],
      onFilter: (value: any, record: LogEntry) => record.level === value,
    },
    {
      title: 'Service',
      dataIndex: 'service',
      key: 'service',
      width: 150,
      render: (service: string) => <Tag>{service}</Tag>,
      filters: [
        { text: 'Training Service', value: 'training_service' },
        { text: 'Simulation Service', value: 'simulation_service' },
        { text: 'Deployment Service', value: 'deployment_service' },
        { text: 'Config Service', value: 'config_service' },
        { text: 'Play Service', value: 'play_service' },
      ],
      onFilter: (value: any, record: LogEntry) => record.service === value,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: { showTitle: false },
      render: (message: string, record: LogEntry) => (
        <Tooltip title={message} placement="topLeft">
          <div style={{ maxWidth: 300 }}>
            {message}
            {record.correlationId && (
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                ID: {record.correlationId}
              </Text>
            )}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (_: any, record: LogEntry) => (
        <Space>
          <Tooltip title="View details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                Modal.info({
                  title: 'Log Entry Details',
                  content: (
                    <div>
                      <Paragraph><strong>Timestamp:</strong> {dayjs(record.timestamp).format('YYYY-MM-DD HH:mm:ss')}</Paragraph>
                      <Paragraph><strong>Level:</strong> {record.level.toUpperCase()}</Paragraph>
                      <Paragraph><strong>Service:</strong> {record.service}</Paragraph>
                      <Paragraph><strong>Message:</strong> {record.message}</Paragraph>
                      {record.correlationId && <Paragraph><strong>Correlation ID:</strong> {record.correlationId}</Paragraph>}
                      {record.userId && <Paragraph><strong>User ID:</strong> {record.userId}</Paragraph>}
                      {record.details && (
                        <div>
                          <Paragraph><strong>Details:</strong></Paragraph>
                          <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                            {JSON.stringify(record.details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  ),
                  width: 600,
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const currentMetrics = metrics[metrics.length - 1] || { 
    cpu: 0, memory: 0, gpu: 0, network: 0, disk: 0, temperature: 0 
  };

  const getSystemMetricsOption = () => {
    return {
      backgroundColor: 'transparent',
      grid: { top: 60, left: 60, right: 60, bottom: 60 },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['CPU', 'Memory', 'GPU', 'Network', 'Disk', 'Temperature'],
        textStyle: { color: '#fff' }
      },
      xAxis: {
        type: 'category',
        data: metrics.map(m => m.timestamp),
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#fff' } }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: { color: '#fff', formatter: '{value}%' },
        axisLine: { lineStyle: { color: '#fff' } },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      series: [
        {
          name: 'CPU',
          type: 'line',
          data: metrics.map(m => m.cpu),
          smooth: true,
          lineStyle: { color: '#52c41a', width: 2 },
          itemStyle: { color: '#52c41a' },
          animation: false
        },
        {
          name: 'Memory',
          type: 'line',
          data: metrics.map(m => m.memory),
          smooth: true,
          lineStyle: { color: '#1890ff', width: 2 },
          itemStyle: { color: '#1890ff' },
          animation: false
        },
        {
          name: 'GPU',
          type: 'line',
          data: metrics.map(m => m.gpu),
          smooth: true,
          lineStyle: { color: '#722ed1', width: 2 },
          itemStyle: { color: '#722ed1' },
          animation: false
        },
        {
          name: 'Network',
          type: 'line',
          data: metrics.map(m => m.network),
          smooth: true,
          lineStyle: { color: '#fa8c16', width: 2 },
          itemStyle: { color: '#fa8c16' },
          animation: false
        },
        {
          name: 'Disk',
          type: 'line',
          data: metrics.map(m => m.disk),
          smooth: true,
          lineStyle: { color: '#eb2f96', width: 2 },
          itemStyle: { color: '#eb2f96' },
          animation: false
        }
      ]
    };
  };

  return (
    <div>
      <div className="section-title">
        <MonitorOutlined />
        {t('monitoring.systemMonitoring')}
        <Badge
          count={systemAlerts.filter(alert => !alert.acknowledged && !alert.resolvedAt).length}
          showZero={false}
          style={{ backgroundColor: '#ff4d4f', marginLeft: '16px' }}
        />
        <div className={`status-indicator ${
          systemStatus.overall === 'healthy' ? 'status-healthy' : 
          systemStatus.overall === 'warning' ? 'status-warning' : 'status-critical'
        }`}>
          {systemStatus.overall}
        </div>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginBottom: '24px' }} size="large">
        <TabPane tab={
          <Space>
            <DashboardOutlined />
            System Overview
          </Space>
        } key="overview">
          {/* System Status Cards */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col xs={24} sm={6}>
              <Card className="dashboard-card">
                <div className="metric-card">
                  <div className="large-number">{systemStatus.uptime}</div>
                  <div className="metric-label">System Uptime</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="dashboard-card">
                <div className="metric-card">
                  <div className="large-number">{systemStatus.healthyServices}/{systemStatus.totalServices}</div>
                  <div className="metric-label">Services Status</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="dashboard-card">
                <div className="metric-card">
                  <div className="large-number" style={{ color: '#cf1322' }}>
                    {systemAlerts.filter(alert => !alert.acknowledged && !alert.resolvedAt).length}
                  </div>
                  <div className="metric-label">{t('monitoring.activeAlerts')}</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="dashboard-card">
                <div className="metric-card">
                  <div className="large-number">{systemStatus.avgResponseTime}ms</div>
                  <div className="metric-label">Avg Response Time</div>
                </div>
              </Card>
            </Col>
          </Row>

          {/* Current System Metrics */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            {[
              { key: 'cpu', label: 'CPU Usage', value: currentMetrics.cpu, color: '#52c41a' },
              { key: 'memory', label: 'Memory', value: currentMetrics.memory, color: '#1890ff' },
              { key: 'gpu', label: 'GPU Usage', value: currentMetrics.gpu, color: '#722ed1' },
              { key: 'network', label: 'Network', value: currentMetrics.network, color: '#fa8c16' },
              { key: 'disk', label: 'Disk Usage', value: currentMetrics.disk, color: '#eb2f96' },
              { key: 'temperature', label: 'Temperature', value: currentMetrics.temperature, color: '#13c2c2', isTemp: true }
            ].map((metric) => (
              <Col xs={24} sm={12} md={8} lg={4} key={metric.key}>
                <Card className="dashboard-card">
                  <div className="progress-circle-container">
                    {metric.isTemp ? (
                      <div style={{ 
                        fontSize: '32px', 
                        fontWeight: 'bold', 
                        color: metric.value > 70 ? '#ff4d4f' : metric.value > 50 ? '#faad14' : metric.color 
                      }}>
                        {metric.value.toFixed(0)}°C
                      </div>
                    ) : (
                      <Progress 
                        type="circle" 
                        percent={Math.round(metric.value)} 
                        width={100}
                        strokeColor={metric.value > 80 ? '#ff4d4f' : metric.value > 60 ? '#faad14' : metric.color}
                        strokeWidth={8}
                      />
                    )}
                    <div className="progress-label">{metric.label}</div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          {/* System Metrics Chart */}
          <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
            <Col span={24}>
              <Card title={
                <Space>
                  <BarChartOutlined />
                  <span>{t('monitoring.systemMetrics')} (Last 30 minutes)</span>
                </Space>
              } className="dashboard-card" extra={
                <Space>
                  <Switch 
                    checked={realTimeEnabled} 
                    onChange={setRealTimeEnabled}
                    checkedChildren="Live"
                    unCheckedChildren="Paused"
                  />
                </Space>
              }>
                <ReactECharts 
                  option={getSystemMetricsOption()}
                  style={{ height: '400px', width: '100%' }}
                  notMerge={true}
                  lazyUpdate={true}
                />
              </Card>
            </Col>
          </Row>

          {/* Service Health Status */}
          <Row>
            <Col span={24}>
              <Card title={
                <Space>
                  <CloudServerOutlined />
                  <span>{t('monitoring.serviceHealth')}</span>
                </Space>
              } className="dashboard-card">
                <Row gutter={[24, 24]}>
                  {serviceHealth.map(service => (
                    <Col xs={24} md={12} lg={8} key={service.name}>
                      <Card 
                        size="small"
                        className="service-status-card"
                        title={
                          <Space>
                            <Badge 
                              status={
                                service.status === 'healthy' ? 'success' :
                                service.status === 'warning' ? 'warning' :
                                service.status === 'critical' ? 'error' : 'default'
                              }
                            />
                            <span style={{ fontSize: '16px', fontWeight: 600 }}>{service.name}</span>
                          </Space>
                        }
                        extra={<Tag>{service.version}</Tag>}
                      >
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div className="service-metric">
                            <span className="service-metric-label">Uptime:</span>
                            <span className="service-metric-value">{service.uptime}</span>
                          </div>
                          <div className="service-metric">
                            <span className="service-metric-label">Last Heartbeat:</span>
                            <span className="service-metric-value">{service.lastHeartbeat}</span>
                          </div>
                          <div className="service-metric">
                            <span className="service-metric-label">Response Time:</span>
                            <span className="service-metric-value">{service.metrics.responseTime.toFixed(0)}ms</span>
                          </div>
                          <div className="service-metric">
                            <span className="service-metric-label">Error Rate:</span>
                            <span className="service-metric-value" style={{ 
                              color: service.metrics.errorRate > 5 ? '#ff4d4f' : '#52c41a' 
                            }}>
                              {service.metrics.errorRate.toFixed(2)}%
                            </span>
                          </div>
                          <div className="service-metric">
                            <span className="service-metric-label">Connections:</span>
                            <span className="service-metric-value">{service.metrics.activeConnections}</span>
                          </div>
                        </Space>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={
          <Space>
            <SearchOutlined />
            {t('monitoring.systemLogs')}
            <Badge count={filteredLogs.filter(log => log.level === 'error' || log.level === 'critical').length} showZero={false} />
          </Space>
        } key="logs">
          <Card title={
            <Space>
              <DatabaseOutlined />
              <span>{t('monitoring.systemLogs')}</span>
              <Text type="secondary">({filteredLogs.length} entries)</Text>
            </Space>
          } extra={
            <Space>
              <RangePicker 
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
              />
              <Search
                placeholder="Search logs..."
                value={logSearchTerm}
                onChange={(e) => setLogSearchTerm(e.target.value)}
                style={{ width: 200 }}
                allowClear
              />
              <Select 
                value={logLevel}
                onChange={setLogLevel}
                style={{ width: 120 }}
              >
                <Option value="all">All Levels</Option>
                <Option value="info">Info</Option>
                <Option value="warning">Warning</Option>
                <Option value="error">Error</Option>
                <Option value="debug">Debug</Option>
                <Option value="critical">Critical</Option>
              </Select>
              <Select 
                value={selectedService}
                onChange={setSelectedService}
                style={{ width: 180 }}
              >
                <Option value="all">All Services</Option>
                <Option value="training_service">Training Service</Option>
                <Option value="simulation_service">Simulation Service</Option>
                <Option value="deployment_service">Deployment Service</Option>
                <Option value="config_service">Config Service</Option>
                <Option value="play_service">Play Service</Option>
              </Select>
              <Button icon={<DownloadOutlined />} onClick={exportLogs}>
                Export
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => {
                generateInitialLogs();
              }}>
                Refresh
              </Button>
            </Space>
          }>
            <Table 
              columns={logColumns}
              dataSource={filteredLogs}
              pagination={{ 
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
              }}
              size="small"
              scroll={{ y: 500 }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <Space>
            <BellOutlined />
            Alerts
            <Badge count={systemAlerts.filter(alert => !alert.acknowledged && !alert.resolvedAt).length} showZero={false} />
          </Space>
        } key="alerts">
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Card title={
                <Space>
                  <BellOutlined />
                  <span>{t('monitoring.activeAlerts')}</span>
                </Space>
              } className="dashboard-card" extra={
                <Space>
                  <Switch 
                    checked={alertsEnabled}
                    onChange={setAlertsEnabled}
                    checkedChildren="Alerts On"
                    unCheckedChildren="Alerts Off"
                  />
                  <Button icon={<SettingOutlined />} onClick={() => setIsSettingsDrawerVisible(true)}>
                    Alert Rules
                  </Button>
                </Space>
              }>
                <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
                  {systemAlerts.filter(alert => !alert.resolvedAt).map(alert => (
                    <div 
                      key={alert.id}
                      className={`alert-item alert-${alert.severity}`}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <div style={{ flex: 1 }}>
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                            <Badge 
                              status={
                                alert.severity === 'critical' ? 'error' :
                                alert.severity === 'warning' ? 'warning' : 'processing'
                              }
                              style={{ marginRight: '12px' }}
                            />
                            <span style={{ fontSize: '18px', fontWeight: 600, color: '#ffffff' }}>
                              {alert.title}
                            </span>
                            <div className={`status-indicator ${
                              alert.severity === 'critical' ? 'status-critical' : 
                              alert.severity === 'warning' ? 'status-warning' : 'status-healthy'
                            }`} style={{ marginLeft: '16px' }}>
                              {alert.severity}
                            </div>
                            {alert.acknowledged && (
                              <div className="status-indicator status-healthy" style={{ marginLeft: '8px' }}>
                                ACKNOWLEDGED
                              </div>
                            )}
                          </div>
                          <div style={{ fontSize: '16px', color: 'rgba(255,255,255,0.8)', marginBottom: '12px' }}>
                            {alert.description}
                          </div>
                          <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.6)' }}>
                            {alert.service} • {dayjs(alert.timestamp).fromNow()}
                          </div>
                        </div>
                        <Space>
                          {!alert.acknowledged && (
                            <Button 
                              onClick={() => acknowledgeAlert(alert.id)}
                            >
                              Acknowledge
                            </Button>
                          )}
                          <Button 
                            type="primary"
                            onClick={() => resolveAlert(alert.id)}
                          >
                            Resolve
                          </Button>
                        </Space>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={
          <Space>
            <BugOutlined />
            Error Monitoring
            <Badge 
              count={0} // This would be dynamically updated based on active errors
              showZero={false} 
              style={{ backgroundColor: '#f5222d' }}
            />
          </Space>
        } key="errors">
          <ErrorMonitoringView zenohClient={zenohClient} />
        </TabPane>
      </Tabs>

      {/* Alert Rules Settings Drawer */}
      <Drawer
        title="Alert Rules Configuration"
        placement="right"
        width={600}
        onClose={() => setIsSettingsDrawerVisible(false)}
        open={isSettingsDrawerVisible}
      >
        <List
          dataSource={alertRules}
          renderItem={rule => (
            <List.Item
              key={rule.id}
              actions={[
                <Switch 
                  checked={rule.enabled}
                  onChange={(checked) => {
                    setAlertRules(prev => prev.map(r => 
                      r.id === rule.id ? { ...r, enabled: checked } : r
                    ));
                  }}
                />
              ]}
            >
              <List.Item.Meta
                title={rule.name}
                description={
                  <Space direction="vertical">
                    <Text>{rule.condition.replace('threshold', rule.threshold.toString())}</Text>
                    <Space>
                      <Tag color={
                        rule.severity === 'critical' ? 'red' :
                        rule.severity === 'warning' ? 'orange' : 'blue'
                      }>
                        {rule.severity.toUpperCase()}
                      </Tag>
                      <Text type="secondary">
                        Notifications: {rule.notifications.join(', ')}
                      </Text>
                    </Space>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Drawer>
    </div>
  );
};

export default MonitoringView;