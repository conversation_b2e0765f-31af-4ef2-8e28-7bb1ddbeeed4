import React, { useState, useEffect } from 'react';
import { <PERSON>, Badge, Tooltip, Typography } from 'antd';
import {
  WifiOutlined,
  DatabaseOutlined,
  HeartOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useZenohStore } from '../../stores/zenoh-store';
import { ConnectionState } from '../../types/zenoh-types';

const { Text } = Typography;

interface SystemStatus {
  zenohConnected: boolean;
  servicesOnline: number;
  totalServices: number;
  lastHeartbeat: Date | null;
  errors: number;
}

const SystemStatusBar: React.FC = () => {
  const { connectionStatus, systemStatus } = useZenohStore();
  const [localStatus, setLocalStatus] = useState<SystemStatus>({
    zenohConnected: false,
    servicesOnline: 0,
    totalServices: 5,
    lastHeartbeat: null,
    errors: 0,
  });

  useEffect(() => {
    // Update local status based on Zenoh connection and system status
    setLocalStatus(prev => ({
      ...prev,
      zenohConnected: connectionStatus.state === ConnectionState.CONNECTED,
      servicesOnline: systemStatus?.active_services?.length || 0,
      totalServices: 5, // This could be dynamic based on system configuration
      lastHeartbeat: connectionStatus.lastConnected || prev.lastHeartbeat,
      errors: systemStatus?.error_count || 0,
    }));
  }, [connectionStatus, systemStatus]);

  const getConnectionStatus = () => {
    switch (connectionStatus.state) {
      case ConnectionState.CONNECTED:
        return { status: 'success' as const, text: '已连接' };
      case ConnectionState.CONNECTING:
        return { status: 'processing' as const, text: '连接中...' };
      case ConnectionState.RECONNECTING:
        return { status: 'warning' as const, text: '重连中...' };
      case ConnectionState.ERROR:
        return { status: 'error' as const, text: '错误' };
      default:
        return { status: 'error' as const, text: '未连接' };
    }
  };

  const getServicesStatus = () => {
    const percentage = localStatus.totalServices > 0 
      ? (localStatus.servicesOnline / localStatus.totalServices) * 100 
      : 0;
      
    if (percentage >= 80) {
      return { status: 'success' as const, color: '#52c41a' };
    } else if (percentage >= 50) {
      return { status: 'warning' as const, color: '#faad14' };
    } else {
      return { status: 'error' as const, color: '#f5222d' };
    }
  };

  const connectionStatusInfo = getConnectionStatus();
  const servicesStatus = getServicesStatus();

  return (
    <Space size="large">
      {/* Zenoh Connection Status */}
      <Tooltip title={`Zenoh路由器: ${connectionStatusInfo.text}${connectionStatus.error ? ` (${connectionStatus.error})` : ''}`}>
        <Space size="small">
          <Badge status={connectionStatusInfo.status} />
          <WifiOutlined style={{ color: localStatus.zenohConnected ? '#52c41a' : '#f5222d' }} />
          <Text style={{ color: 'white' }}>Zenoh</Text>
        </Space>
      </Tooltip>

      {/* Services Status */}
      <Tooltip title={`在线服务: ${localStatus.servicesOnline}/${localStatus.totalServices}`}>
        <Space size="small">
          <Badge
            count={localStatus.servicesOnline}
            overflowCount={99}
            style={{ backgroundColor: servicesStatus.color }}
          />
          <DatabaseOutlined style={{ color: servicesStatus.color }} />
          <Text style={{ color: 'white' }}>
            {localStatus.servicesOnline}/{localStatus.totalServices}
          </Text>
        </Space>
      </Tooltip>

      {/* Heartbeat */}
      <Tooltip title={`最后心跳: ${localStatus.lastHeartbeat?.toLocaleTimeString() || '从未'}`}>
        <Space size="small">
          <HeartOutlined
            style={{
              color: localStatus.lastHeartbeat ? '#52c41a' : '#f5222d',
              animation: localStatus.zenohConnected ? 'heartbeat 2s infinite' : 'none'
            }}
          />
          <Text style={{ color: 'white' }}>
            {localStatus.lastHeartbeat?.toLocaleTimeString() || '--:--:--'}
          </Text>
        </Space>
      </Tooltip>

      {/* Error Count */}
      {localStatus.errors > 0 && (
        <Tooltip title={`活跃错误: ${localStatus.errors}`}>
          <Space size="small">
            <Badge count={localStatus.errors} />
            <WarningOutlined style={{ color: '#faad14' }} />
          </Space>
        </Tooltip>
      )}

      {/* Connection Attempts (when reconnecting) */}
      {connectionStatus.state === ConnectionState.RECONNECTING && (
        <Tooltip title={`重连尝试: ${connectionStatus.reconnectAttempts}`}>
          <Text style={{ color: '#faad14' }}>
            重试: {connectionStatus.reconnectAttempts}
          </Text>
        </Tooltip>
      )}
    </Space>
  );
};

export default SystemStatusBar;