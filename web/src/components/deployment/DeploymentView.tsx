import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Card, Form, Input, Select, Button, Typography, Space, Table, Tag, Progress, Upload, message, Tabs, Modal, Alert, List, Tooltip, Badge, Drawer, Descriptions } from 'antd';
import {
  CloudUploadOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InboxOutlined,
  RocketOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DeleteOutlined,
  HistoryOutlined,
  EyeOutlined,
  WarningOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;
const { Dragger } = Upload;
const { TabPane } = Tabs;

interface DeploymentViewProps {
  zenohClient?: any;
}

interface DeploymentJob {
  key: string;
  id: string;
  modelName: string;
  format: string;
  platform: string;
  status: 'idle' | 'preparing' | 'exporting' | 'validating' | 'deploying' | 'completed' | 'error';
  progress: number;
  size: string;
  createdAt: string;
  error?: string;
  validationResults?: ValidationResult;
  exportPath?: string;
  startTime?: number;
  estimatedTimeRemaining?: number;
}

interface ModelInfo {
  name: string;
  path: string;
  size: number;
  format: string;
  createdAt: string;
  hash?: string;
  version?: string;
  description?: string;
  performance?: Record<string, number>;
  isDeployed?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  modelFormat: string;
  modelSizeMb: number;
  inputShapes: Record<string, number[]>;
  outputShapes: Record<string, number[]>;
  inferenceTimeMs: number;
  validationErrors: string[];
  compatibilityInfo: Record<string, any>;
}

interface DeploymentLogs {
  jobId: string;
  logs: LogEntry[];
}

interface LogEntry {
  timestamp: number;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: any;
}

const DeploymentView: React.FC<DeploymentViewProps> = ({ zenohClient }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isExporting, setIsExporting] = useState(false);
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [selectedModel, setSelectedModel] = useState<ModelInfo | null>(null);
  const [deploymentLogs, setDeploymentLogs] = useState<Record<string, LogEntry[]>>({});
  const [isLogsDrawerVisible, setIsLogsDrawerVisible] = useState(false);
  const [selectedJobLogs, setSelectedJobLogs] = useState<string>('');
  const [isValidationModalVisible, setIsValidationModalVisible] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationResult | null>(null);
  const [serviceStatus, setServiceStatus] = useState({
    isConnected: false,
    activeJobs: 0,
    availableFormats: ['jit', 'onnx']
  });

  const [deploymentJobs, setDeploymentJobs] = useState<DeploymentJob[]>([]);
  const [activeTab, setActiveTab] = useState('export');

  // Load available models on mount
  useEffect(() => {
    loadAvailableModels();
    if (zenohClient) {
      setupZenohSubscriptions();
    }
  }, [zenohClient]);

  const loadAvailableModels = useCallback(async () => {
    if (!zenohClient || !zenohClient.isConnected()) {
      console.warn('Zenoh client not connected');
      return;
    }

    try {
      // Send command to get available models
      zenohClient.sendCommand(
        'legged_gym/deployment/command',
        'list_models',
        {}
      );
    } catch (error) {
      console.error('Failed to load models:', error);
      message.error('Failed to load available models');
    }
  }, [zenohClient]);

  const setupZenohSubscriptions = useCallback(() => {
    if (!zenohClient) return;

    // Subscribe to deployment status updates
    const statusSubscription = zenohClient.subscribe(
      'legged_gym/deployment/status',
      handleDeploymentStatusUpdate
    );

    // Subscribe to model info updates
    const modelSubscription = zenohClient.subscribe(
      'legged_gym/deployment/model/info',
      handleModelInfoUpdate
    );

    // Cleanup on unmount
    return () => {
      zenohClient.unsubscribe(statusSubscription);
      zenohClient.unsubscribe(modelSubscription);
    };
  }, [zenohClient]);

  const handleDeploymentStatusUpdate = useCallback((message: any) => {
    try {
      const statusData = message.payload;
      
      if (statusData.job_id) {
        // Update specific job status
        setDeploymentJobs(prev => prev.map(job => 
          job.id === statusData.job_id 
            ? {
                ...job,
                status: statusData.status || job.status,
                progress: statusData.progress || job.progress,
                error: statusData.error_message,
                validationResults: statusData.validation_result
              }
            : job
        ));

        // Add log entry
        const logEntry: LogEntry = {
          timestamp: Date.now(),
          level: statusData.status === 'error' ? 'error' : 'info',
          message: statusData.message || `Job ${statusData.status}`,
          details: statusData
        };
        
        setDeploymentLogs(prev => ({
          ...prev,
          [statusData.job_id]: [...(prev[statusData.job_id] || []), logEntry]
        }));
      } else if (statusData.available_models) {
        // Update available models list
        const models: ModelInfo[] = Object.values(statusData.available_models).map((model: any) => ({
          name: model.model_name,
          path: model.model_path,
          size: model.file_size,
          format: model.model_format,
          createdAt: new Date(model.creation_time * 1000).toLocaleString(),
          performance: model.performance_metrics
        }));
        setAvailableModels(models);
      }
    } catch (error) {
      console.error('Error handling deployment status update:', error);
    }
  }, []);

  const handleModelInfoUpdate = useCallback((message: any) => {
    try {
      const modelData = message.payload;
      const modelInfo: ModelInfo = {
        name: modelData.model_name,
        path: modelData.model_path,
        size: modelData.file_size,
        format: modelData.model_format,
        createdAt: new Date(modelData.creation_time * 1000).toLocaleString(),
        performance: modelData.performance_metrics,
        isDeployed: true
      };

      setAvailableModels(prev => {
        const existingIndex = prev.findIndex(m => m.path === modelInfo.path);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = modelInfo;
          return updated;
        } else {
          return [...prev, modelInfo];
        }
      });
    } catch (error) {
      console.error('Error handling model info update:', error);
    }
  }, []);

  const handleValidateModel = useCallback(async (modelPath: string) => {
    if (!zenohClient || !zenohClient.isConnected()) {
      message.error('Not connected to deployment service');
      return;
    }

    try {
      zenohClient.sendCommand(
        'legged_gym/deployment/command',
        'validate',
        { model_path: modelPath }
      );
      message.info('Model validation started');
    } catch (error) {
      console.error('Failed to start validation:', error);
      message.error('Failed to start model validation');
    }
  }, [zenohClient]);

  const handleCancelJob = useCallback(async (jobId: string) => {
    if (!zenohClient || !zenohClient.isConnected()) {
      message.error('Not connected to deployment service');
      return;
    }

    try {
      zenohClient.sendCommand(
        'legged_gym/deployment/command',
        'cancel',
        { job_id: jobId }
      );
      message.info('Cancellation request sent');
    } catch (error) {
      console.error('Failed to cancel job:', error);
      message.error('Failed to cancel job');
    }
  }, [zenohClient]);

  const showJobLogs = useCallback((jobId: string) => {
    setSelectedJobLogs(jobId);
    setIsLogsDrawerVisible(true);
  }, []);

  const showValidationResults = useCallback((results: ValidationResult) => {
    setValidationResults(results);
    setIsValidationModalVisible(true);
  }, []);

  const columns = [
    {
      title: 'Job ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Model Name',
      dataIndex: 'modelName',
      key: 'modelName',
    },
    {
      title: 'Format',
      dataIndex: 'format',
      key: 'format',
      render: (format: string) => <Tag>{format}</Tag>,
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform: string) => (
        <Tag color={platform === 'GPU' ? 'blue' : 'green'}>{platform}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: DeploymentJob) => {
        const statusConfig = {
          idle: { color: 'default', icon: null },
          preparing: { color: 'processing', icon: <ClockCircleOutlined spin /> },
          exporting: { color: 'processing', icon: <CloudUploadOutlined spin /> },
          validating: { color: 'processing', icon: <CheckCircleOutlined spin /> },
          deploying: { color: 'processing', icon: <RocketOutlined spin /> },
          completed: { color: 'success', icon: <CheckCircleOutlined /> },
          error: { color: 'error', icon: <ExclamationCircleOutlined /> },
        }[status] || { color: 'default', icon: null };

        return (
          <Space>
            <Tag color={statusConfig.color} icon={statusConfig.icon}>
              {status.toUpperCase()}
            </Tag>
            {record.error && (
              <Tooltip title={record.error}>
                <WarningOutlined style={{ color: '#ff4d4f' }} />
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: DeploymentJob) => {
        const { status, estimatedTimeRemaining } = record;
        let progressStatus: 'success' | 'exception' | 'active' | 'normal' = 'normal';
        
        if (status === 'completed') {
          progressStatus = 'success';
          progress = 100;
        } else if (status === 'error') {
          progressStatus = 'exception';
          progress = 0;
        } else if (['preparing', 'exporting', 'validating', 'deploying'].includes(status)) {
          progressStatus = 'active';
        }
        
        return (
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Progress 
              percent={Math.round(progress)} 
              size="small" 
              status={progressStatus}
              format={(percent) => `${percent}%`}
            />
            {estimatedTimeRemaining && status !== 'completed' && status !== 'error' && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                ~{Math.round(estimatedTimeRemaining)}s remaining
              </Text>
            )}
          </Space>
        );
      },
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_: any, record: DeploymentJob) => (
        <Space wrap>
          {record.status === 'completed' && record.exportPath && (
            <>
              <Tooltip title="Download exported model">
                <Button 
                  type="text" 
                  icon={<DownloadOutlined />}
                  size="small"
                  onClick={() => {
                    // Trigger download
                    const link = document.createElement('a');
                    link.href = `/api/download/${encodeURIComponent(record.exportPath!)}`;
                    link.download = record.modelName;
                    link.click();
                  }}
                />
              </Tooltip>
              <Tooltip title="Deploy model">
                <Button 
                  type="text" 
                  icon={<RocketOutlined />}
                  size="small"
                  disabled
                />
              </Tooltip>
            </>
          )}
          {record.validationResults && (
            <Tooltip title="View validation results">
              <Button 
                type="text" 
                icon={<EyeOutlined />}
                size="small"
                onClick={() => showValidationResults(record.validationResults!)}
              />
            </Tooltip>
          )}
          <Tooltip title="View logs">
            <Button 
              type="text" 
              icon={<HistoryOutlined />}
              size="small"
              onClick={() => showJobLogs(record.id)}
            />
          </Tooltip>
          {['preparing', 'exporting', 'validating', 'deploying'].includes(record.status) && (
            <Tooltip title="Cancel job">
              <Button 
                type="text" 
                icon={<StopOutlined />}
                size="small"
                danger
                onClick={() => handleCancelJob(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="Delete job">
            <Button 
              type="text" 
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => {
                setDeploymentJobs(prev => prev.filter(job => job.id !== record.id));
                message.success('Job deleted');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleStartExport = async (values: any) => {
    if (!zenohClient || !zenohClient.isConnected()) {
      message.error('Not connected to deployment service');
      return;
    }

    setIsExporting(true);
    
    try {
      // Send export command to backend
      const exportConfig = {
        model_path: values.modelPath,
        export_format: values.exportFormat,
        export_path: values.customPath || '',
        target_platform: values.targetPlatform,
        optimization_level: values.optimizationLevel,
        quantization: values.quantization || false
      };

      zenohClient.sendCommand(
        'legged_gym/deployment/command',
        'export',
        exportConfig
      );

      // Create job entry (will be updated via Zenoh messages)
      const newJob: DeploymentJob = {
        key: String(Date.now()),
        id: `export_${Date.now()}`,
        modelName: values.modelPath?.split('/').pop()?.replace('.pt', '') || 'unknown_model',
        format: values.exportFormat.toUpperCase(),
        platform: values.targetPlatform.toUpperCase(),
        status: 'preparing',
        progress: 0,
        size: '0 MB',
        createdAt: new Date().toLocaleString(),
        startTime: Date.now()
      };

      setDeploymentJobs(prev => [newJob, ...prev]);
      message.success('Export job started successfully!');
      
    } catch (error) {
      console.error('Failed to start export:', error);
      message.error('Failed to start model export');
    } finally {
      setIsExporting(false);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.pt,.pth,.onnx',
    action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
    onChange(info: any) {
      const { status } = info.file;
      if (status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        message.success(`${info.file.name} file uploaded successfully.`);
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        <Space>
          {t('deployment.modelDeployment')}
          <Badge
            count={serviceStatus.activeJobs}
            showZero
            style={{ backgroundColor: '#52c41a' }}
          />
          {serviceStatus.isConnected ? (
            <Tag color="green">{t('deployment.connected')}</Tag>
          ) : (
            <Tag color="red">{t('deployment.disconnected')}</Tag>
          )}
        </Space>
      </Title>

      <Tabs defaultActiveKey="export" style={{ marginBottom: '24px' }}>
        <TabPane tab={t('deployment.exportConfig')} key="export">
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {/* Model Export Configuration */}
            <Col xs={24} lg={12}>
              <Card title={
                <Space>
                  <CloudUploadOutlined />
                  <span>{t('deployment.exportConfig')}</span>
                </Space>
              }>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleStartExport}
                  initialValues={{
                    exportFormat: 'onnx',
                    targetPlatform: 'gpu',
                    optimizationLevel: 'default',
                    quantization: false,
                  }}
                >
                  <Form.Item
                    label={t('deployment.selectModel')}
                    name="modelPath"
                    rules={[{ required: true, message: t('deployment.pleaseSelectModel') }]}
                  >
                    <Select 
                      placeholder={t('deployment.selectTrainedModel')}
                      onChange={(value) => {
                        const model = availableModels.find(m => m.path === value);
                        setSelectedModel(model || null);
                      }}
                      loading={availableModels.length === 0}
                      dropdownRender={(menu) => (
                        <div>
                          {menu}
                          <div style={{ padding: '8px', borderTop: '1px solid #f0f0f0' }}>
                            <Button 
                              type="link" 
                              size="small"
                              icon={<ReloadOutlined />}
                              onClick={loadAvailableModels}
                            >
                              {t('deployment.refreshModels')}
                            </Button>
                          </div>
                        </div>
                      )}
                    >
                      {availableModels.map(model => (
                        <Option key={model.path} value={model.path}>
                          <div>
                            <div>{model.name}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {model.format.toUpperCase()} • {(model.size / 1024 / 1024).toFixed(1)}MB • {model.createdAt}
                            </Text>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  {selectedModel && (
                    <Alert
                      message={t('deployment.modelInformation')}
                      description={
                        <Space direction="vertical" size="small">
                          <Text><strong>{t('deployment.format')}:</strong> {selectedModel.format}</Text>
                          <Text><strong>{t('deployment.size')}:</strong> {(selectedModel.size / 1024 / 1024).toFixed(2)} MB</Text>
                          <Text><strong>{t('deployment.created')}:</strong> {selectedModel.createdAt}</Text>
                          {selectedModel.performance && Object.keys(selectedModel.performance).length > 0 && (
                            <div>
                              <Text strong>Performance:</Text>
                              <ul>
                                {Object.entries(selectedModel.performance).map(([key, value]) => (
                                  <li key={key}>
                                    <Text>{key}: {typeof value === 'number' ? value.toFixed(4) : value}</Text>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                          <Button 
                            size="small"
                            icon={<CheckCircleOutlined />}
                            onClick={() => handleValidateModel(selectedModel.path)}
                          >
                            {t('deployment.validateModel')}
                          </Button>
                        </Space>
                      }
                      type="info"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                  )}

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label={t('deployment.exportFormat')}
                        name="exportFormat"
                        rules={[{ required: true, message: t('deployment.pleaseSelectExportFormat') }]}
                      >
                        <Select>
                          {serviceStatus.availableFormats.includes('onnx') && (
                            <Option value="onnx">ONNX (.onnx)</Option>
                          )}
                          {serviceStatus.availableFormats.includes('jit') && (
                            <Option value="jit">TorchScript JIT (.pt)</Option>
                          )}
                          {serviceStatus.availableFormats.includes('tensorrt') && (
                            <Option value="tensorrt">TensorRT (.trt)</Option>
                          )}
                          {serviceStatus.availableFormats.includes('openvino') && (
                            <Option value="openvino">OpenVINO (.xml)</Option>
                          )}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label={t('deployment.targetPlatform')}
                        name="targetPlatform"
                        rules={[{ required: true, message: t('deployment.pleaseSelectTargetPlatform') }]}
                      >
                        <Select>
                          <Option value="cpu">{t('deployment.cpu')}</Option>
                          <Option value="gpu">{t('deployment.gpu')}</Option>
                          <Option value="edge">{t('deployment.edge')}</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label={t('deployment.optimizationLevel')}
                    name="optimizationLevel"
                  >
                    <Select>
                      <Option value="none">{t('deployment.none')} - No optimization</Option>
                      <Option value="default">{t('deployment.basic')} - Standard optimization</Option>
                      <Option value="aggressive">{t('deployment.aggressive')} - Maximum optimization</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    label="Custom Export Path (Optional)"
                    name="customPath"
                  >
                    <Input placeholder="Leave empty for auto-generated path" />
                  </Form.Item>
                  
                  <Form.Item
                    name="quantization"
                    valuePropName="checked"
                  >
                    <Button type="dashed" style={{ width: '100%' }}>
                      Enable Quantization (Experimental)
                    </Button>
                  </Form.Item>

                  <Form.Item>
                    <Button 
                      type="primary" 
                      htmlType="submit"
                      icon={<CloudUploadOutlined />}
                      loading={isExporting}
                      block
                    >
                      Start Export
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            {/* Model Upload */}
            <Col xs={24} lg={12}>
              <Card title={
                <Space>
                  <InboxOutlined />
                  <span>Upload Model</span>
                </Space>
              }>
                <Dragger {...uploadProps} style={{ marginBottom: '16px' }}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    Click or drag model file to this area to upload
                  </p>
                  <p className="ant-upload-hint">
                    Support for .pt, .pth, .onnx files. Single file upload only.
                  </p>
                </Dragger>

                <div style={{ marginTop: '24px' }}>
                  <Title level={5}>Deployment Statistics</Title>
                  <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                          {availableModels.filter(m => m.isDeployed).length}
                        </div>
                        <Text type="secondary">Models Deployed</Text>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                          {(availableModels.reduce((sum, m) => sum + m.size, 0) / 1024 / 1024).toFixed(0)} MB
                        </div>
                        <Text type="secondary">Total Size</Text>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#722ed1' }}>
                          {deploymentJobs.length > 0 ? Math.round((deploymentJobs.filter(j => j.status === 'completed').length / deploymentJobs.length) * 100) : 0}%
                        </div>
                        <Text type="secondary">Success Rate</Text>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#fa8c16' }}>
                          {serviceStatus.activeJobs}
                        </div>
                        <Text type="secondary">Active Jobs</Text>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
        
        <TabPane tab={t('deployment.performanceTesting')} key="testing">
          <Card title={t('deployment.modelPerformanceTesting')}>
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card 
                  title="Inference Performance Test"
                  size="small"
                >
                  <Form layout="vertical">
                    <Form.Item label="Select Model for Testing">
                      <Select placeholder="Choose a model to test">
                        {availableModels.map(model => (
                          <Option key={model.path} value={model.path}>
                            {model.name} ({model.format.toUpperCase()})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="Test Iterations">
                      <Select defaultValue="100">
                        <Option value="10">10 iterations</Option>
                        <Option value="100">100 iterations</Option>
                        <Option value="1000">1000 iterations</Option>
                        <Option value="5000">5000 iterations</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="Batch Size">
                      <Select defaultValue="1">
                        <Option value="1">1</Option>
                        <Option value="4">4</Option>
                        <Option value="8">8</Option>
                        <Option value="16">16</Option>
                        <Option value="32">32</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item>
                      <Button type="primary" block disabled>
                        Start Performance Test
                      </Button>
                    </Form.Item>
                  </Form>
                  
                  {/* Performance Results */}
                  <div style={{ marginTop: 16 }}>
                    <Alert 
                      message="Performance Test Results"
                      description={
                        <Row gutter={16}>
                          <Col span={12}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                                2.3ms
                              </div>
                              <Text type="secondary">Avg Inference Time</Text>
                            </div>
                          </Col>
                          <Col span={12}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#52c41a' }}>
                                434 FPS
                              </div>
                              <Text type="secondary">Throughput</Text>
                            </div>
                          </Col>
                          <Col span={12}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#722ed1' }}>
                                128MB
                              </div>
                              <Text type="secondary">Memory Usage</Text>
                            </div>
                          </Col>
                          <Col span={12}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#fa8c16' }}>
                                0.12ms
                              </div>
                              <Text type="secondary">Std Deviation</Text>
                            </div>
                          </Col>
                        </Row>
                      }
                      type="info"
                    />
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} lg={12}>
                <Card 
                  title="Accuracy Validation"
                  size="small"
                >
                  <Form layout="vertical">
                    <Form.Item label="Test Dataset">
                      <Select placeholder="Choose test dataset">
                        <Option value="validation">Validation Set</Option>
                        <Option value="test">Test Set</Option>
                        <Option value="custom">Custom Dataset</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="Metrics">
                      <Select mode="multiple" placeholder="Select metrics to compute">
                        <Option value="reward">Average Reward</Option>
                        <Option value="success">Success Rate</Option>
                        <Option value="stability">Action Stability</Option>
                        <Option value="tracking">Trajectory Tracking</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="Number of Episodes">
                      <Select defaultValue="50">
                        <Option value="10">10 episodes</Option>
                        <Option value="50">50 episodes</Option>
                        <Option value="100">100 episodes</Option>
                        <Option value="500">500 episodes</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item>
                      <Button type="primary" block disabled>
                        Start Accuracy Test
                      </Button>
                    </Form.Item>
                  </Form>
                  
                  {/* Accuracy Results */}
                  <div style={{ marginTop: 16 }}>
                    <Alert 
                      message="Accuracy Test Results"
                      description={
                        <List
                          size="small"
                          dataSource={[
                            { metric: 'Average Reward', value: '847.3 ± 23.1' },
                            { metric: 'Success Rate', value: '94.2%' },
                            { metric: 'Action Stability', value: '0.95' },
                            { metric: 'Trajectory Tracking', value: '0.87' }
                          ]}
                          renderItem={item => (
                            <List.Item>
                              <Text strong>{item.metric}:</Text> <Text>{item.value}</Text>
                            </List.Item>
                          )}
                        />
                      }
                      type="success"
                    />
                  </div>
                </Card>
              </Col>
              
              <Col span={24}>
                <Card title="Model Comparison" size="small">
                  <Table
                    size="small"
                    columns={[
                      { title: 'Model', dataIndex: 'model', key: 'model' },
                      { title: 'Format', dataIndex: 'format', key: 'format' },
                      { title: 'Inference Time (ms)', dataIndex: 'inferenceTime', key: 'inferenceTime', sorter: true },
                      { title: 'Memory (MB)', dataIndex: 'memory', key: 'memory', sorter: true },
                      { title: 'Accuracy', dataIndex: 'accuracy', key: 'accuracy', sorter: true },
                      { title: 'Size (MB)', dataIndex: 'size', key: 'size', sorter: true }
                    ]}
                    dataSource={[
                      {
                        key: '1',
                        model: 'anymal_c_v2.1',
                        format: 'ONNX',
                        inferenceTime: 2.3,
                        memory: 128,
                        accuracy: '94.2%',
                        size: 45.2
                      },
                      {
                        key: '2',
                        model: 'anymal_c_v2.1',
                        format: 'JIT',
                        inferenceTime: 1.8,
                        memory: 156,
                        accuracy: '94.5%',
                        size: 52.1
                      },
                      {
                        key: '3',
                        model: 'anymal_c_v1.8',
                        format: 'ONNX',
                        inferenceTime: 3.1,
                        memory: 112,
                        accuracy: '92.1%',
                        size: 38.7
                      }
                    ]}
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </TabPane>
        
        <TabPane tab={t('deployment.versionManagement')} key="versions">
          <Card title={t('deployment.modelVersionManagement')}>
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={16}>
                <Card title="Version History" size="small">
                  <Table
                    size="small"
                    columns={[
                      { 
                        title: 'Version', 
                        dataIndex: 'version', 
                        key: 'version',
                        render: (version, record) => (
                          <Space>
                            <Tag color={record.isCurrent ? 'green' : 'default'}>
                              v{version}
                            </Tag>
                            {record.isCurrent && <Badge status="success" text="Current" />}
                          </Space>
                        )
                      },
                      { title: 'Model', dataIndex: 'model', key: 'model' },
                      { 
                        title: 'Created', 
                        dataIndex: 'created', 
                        key: 'created',
                        sorter: true 
                      },
                      { 
                        title: 'Performance', 
                        dataIndex: 'performance', 
                        key: 'performance',
                        render: (perf) => <Tag color="blue">{perf}%</Tag>
                      },
                      { 
                        title: 'Size', 
                        dataIndex: 'size', 
                        key: 'size',
                        render: (size) => `${size} MB`
                      },
                      {
                        title: 'Actions',
                        key: 'actions',
                        render: (_, record) => (
                          <Space>
                            {!record.isCurrent && (
                              <Button size="small" type="link">
                                Rollback
                              </Button>
                            )}
                            <Button size="small" type="link">
                              Compare
                            </Button>
                            <Button size="small" type="link" danger>
                              Delete
                            </Button>
                          </Space>
                        )
                      }
                    ]}
                    dataSource={[
                      {
                        key: '1',
                        version: '2.1.3',
                        model: 'anymal_c_rough_terrain',
                        created: '2024-01-20 10:30:00',
                        performance: 94.2,
                        size: 45.2,
                        isCurrent: true
                      },
                      {
                        key: '2',
                        version: '2.1.2',
                        model: 'anymal_c_rough_terrain',
                        created: '2024-01-18 14:15:00',
                        performance: 93.8,
                        size: 44.8,
                        isCurrent: false
                      },
                      {
                        key: '3',
                        version: '2.1.1',
                        model: 'anymal_c_rough_terrain',
                        created: '2024-01-15 09:20:00',
                        performance: 92.5,
                        size: 43.1,
                        isCurrent: false
                      },
                      {
                        key: '4',
                        version: '2.0.0',
                        model: 'anymal_c_rough_terrain',
                        created: '2024-01-10 16:45:00',
                        performance: 89.7,
                        size: 42.3,
                        isCurrent: false
                      }
                    ]}
                    pagination={{ pageSize: 10 }}
                  />
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Card title="Version Analytics" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                            12
                          </div>
                          <Text type="secondary">Total Versions</Text>
                        </div>
                      </Col>
                      <Col span={12}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                            v2.1.3
                          </div>
                          <Text type="secondary">Latest Version</Text>
                        </div>
                      </Col>
                      <Col span={12}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#722ed1' }}>
                            4.5%
                          </div>
                          <Text type="secondary">Performance Gain</Text>
                        </div>
                      </Col>
                      <Col span={12}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#fa8c16' }}>
                            3 days
                          </div>
                          <Text type="secondary">Avg Update Freq</Text>
                        </div>
                      </Col>
                    </Row>
                  </Card>
                  
                  <Card title="Deployment Status" size="small">
                    <List
                      size="small"
                      dataSource={[
                        { env: 'Production', version: 'v2.1.2', status: 'active' },
                        { env: 'Staging', version: 'v2.1.3', status: 'testing' },
                        { env: 'Development', version: 'v2.2.0-beta', status: 'development' }
                      ]}
                      renderItem={item => (
                        <List.Item>
                          <Space>
                            <Text strong>{item.env}:</Text>
                            <Tag color={
                              item.status === 'active' ? 'green' : 
                              item.status === 'testing' ? 'blue' : 'orange'
                            }>
                              {item.version}
                            </Tag>
                            <Badge 
                              status={
                                item.status === 'active' ? 'success' : 
                                item.status === 'testing' ? 'processing' : 'warning'
                              } 
                              text={item.status}
                            />
                          </Space>
                        </List.Item>
                      )}
                    />
                  </Card>
                  
                  <Card title="Quick Actions" size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button type="primary" block>
                        Create New Version
                      </Button>
                      <Button block>
                        Compare Versions
                      </Button>
                      <Button block>
                        Rollback to Previous
                      </Button>
                      <Button block danger>
                        Clean Old Versions
                      </Button>
                    </Space>
                  </Card>
                </Space>
              </Col>
              
              <Col span={24}>
                <Card title="Performance Trend" size="small">
                  <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#fafafa' }}>
                    <Text type="secondary">Performance trend chart would be displayed here</Text>
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </TabPane>
      </Tabs>

      {/* Deployment Management Table */}
      <Row>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <span>Deployment Management</span>
                <Badge count={deploymentJobs.filter(job => ['preparing', 'exporting', 'validating', 'deploying'].includes(job.status)).length} />
                {!serviceStatus.isConnected && (
                  <Tag color="red">Service Disconnected</Tag>
                )}
              </Space>
            }
            extra={
              <Space>
                <Button 
                  icon={<ReloadOutlined />}
                  size="small"
                  onClick={() => {
                    loadAvailableModels();
                    message.info('Refreshed deployment status');
                  }}
                >
                  Refresh
                </Button>
              </Space>
            }
          >
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab={t('deployment.activeJobsTab')} key="jobs">
                <Table 
                  columns={columns} 
                  dataSource={deploymentJobs.filter(job => !['completed', 'error'].includes(job.status))}
                  pagination={{ pageSize: 5 }}
                  size="small"
                  locale={{ emptyText: t('deployment.noActiveDeploymentJobs') }}
                />
              </TabPane>
              <TabPane tab={t('deployment.jobHistory')} key="history">
                <Table 
                  columns={columns} 
                  dataSource={deploymentJobs.filter(job => ['completed', 'error'].includes(job.status))}
                  pagination={{ pageSize: 10 }}
                  size="small"
                  locale={{ emptyText: t('deployment.noDeploymentHistory') }}
                />
              </TabPane>
              <TabPane tab={t('deployment.modelVersions')} key="versions">
                <List
                  dataSource={availableModels}
                  renderItem={(model) => (
                    <List.Item
                      actions={[
                        <Button 
                          size="small" 
                          onClick={() => handleValidateModel(model.path)}
                        >
                          {t('deployment.validate')}
                        </Button>,
                        <Button 
                          size="small"
                          type="primary"
                          onClick={() => {
                            form.setFieldsValue({ modelPath: model.path });
                            setSelectedModel(model);
                            setActiveTab('export');
                          }}
                        >
                          {t('deployment.export')}
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        title={model.name}
                        description={
                          <Space direction="vertical" size="small">
                            <Text type="secondary">
                              {model.format.toUpperCase()} • {(model.size / 1024 / 1024).toFixed(1)}MB • {model.createdAt}
                            </Text>
                            {model.description && <Text>{model.description}</Text>}
                            {model.isDeployed && <Tag color="green">{t('deployment.deployed')}</Tag>}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
      
      {/* Logs Drawer */}
      <Drawer
        title={`${t('deployment.deploymentLogs')} - Job ${selectedJobLogs}`}
        placement="right"
        width={600}
        onClose={() => setIsLogsDrawerVisible(false)}
        open={isLogsDrawerVisible}
      >
        <List
          dataSource={deploymentLogs[selectedJobLogs] || []}
          renderItem={(log, index) => (
            <List.Item key={index}>
              <List.Item.Meta
                avatar={
                  <Tag color={log.level === 'error' ? 'red' : log.level === 'warning' ? 'orange' : 'blue'}>
                    {log.level.toUpperCase()}
                  </Tag>
                }
                title={new Date(log.timestamp).toLocaleString()}
                description={log.message}
              />
              {log.details && (
                <pre style={{ fontSize: '12px', color: '#666' }}>
                  {JSON.stringify(log.details, null, 2)}
                </pre>
              )}
            </List.Item>
          )}
        />
      </Drawer>
      
      {/* Validation Results Modal */}
      <Modal
        title={t('deployment.modelValidationResults')}
        open={isValidationModalVisible}
        onCancel={() => setIsValidationModalVisible(false)}
        width={700}
        footer={[
          <Button key="close" onClick={() => setIsValidationModalVisible(false)}>
            {t('deployment.close')}
          </Button>
        ]}
      >
        {validationResults && (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Alert
              message={validationResults.isValid ? t('deployment.modelValid') : t('deployment.validationFailed')}
              type={validationResults.isValid ? "success" : "error"}
              showIcon
            />
            
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="Format">{validationResults.modelFormat}</Descriptions.Item>
              <Descriptions.Item label="Size">{validationResults.modelSizeMb.toFixed(2)} MB</Descriptions.Item>
              <Descriptions.Item label="Inference Time">{validationResults.inferenceTimeMs.toFixed(2)} ms</Descriptions.Item>
              <Descriptions.Item label="Valid">{validationResults.isValid ? 'Yes' : 'No'}</Descriptions.Item>
            </Descriptions>
            
            {Object.keys(validationResults.inputShapes).length > 0 && (
              <div>
                <Title level={5}>Input Shapes</Title>
                <pre>{JSON.stringify(validationResults.inputShapes, null, 2)}</pre>
              </div>
            )}
            
            {Object.keys(validationResults.outputShapes).length > 0 && (
              <div>
                <Title level={5}>Output Shapes</Title>
                <pre>{JSON.stringify(validationResults.outputShapes, null, 2)}</pre>
              </div>
            )}
            
            {validationResults.validationErrors.length > 0 && (
              <div>
                <Title level={5}>Validation Errors</Title>
                <List
                  dataSource={validationResults.validationErrors}
                  renderItem={(error, index) => (
                    <List.Item key={index}>
                      <Text type="danger">{error}</Text>
                    </List.Item>
                  )}
                />
              </div>
            )}
          </Space>
        )}
      </Modal>
    </div>
  );
};

export default DeploymentView;