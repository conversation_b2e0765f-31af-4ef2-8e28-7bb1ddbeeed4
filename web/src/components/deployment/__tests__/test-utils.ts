import { MockedFunction } from 'jest-mock';

// Mock Zenoh Client for testing
export interface MockZenohClient {
  isConnected: jest.MockedFunction<() => boolean>;
  sendCommand: jest.MockedFunction<(topic: string, command: string, parameters?: any) => void>;
  subscribe: jest.MockedFunction<(topic: string, callback: (message: any) => void) => string>;
  unsubscribe: jest.MockedFunction<(subscriptionId: string) => void>;
}

export const createMockZenohClient = (connected = true): MockZenohClient => ({
  isConnected: jest.fn(() => connected) as jest.MockedFunction<() => boolean>,
  sendCommand: jest.fn() as jest.MockedFunction<(topic: string, command: string, parameters?: any) => void>,
  subscribe: jest.fn().mockReturnValue('mock-subscription-id') as jest.MockedFunction<(topic: string, callback: (message: any) => void) => string>,
  unsubscribe: jest.fn() as jest.MockedFunction<(subscriptionId: string) => void>,
});

// Mock model data
export const mockModelData = {
  anymal_c_rough: {
    model_name: 'anymal_c_rough_terrain',
    model_path: '/models/anymal_c_rough_terrain_v2.1.pt',
    file_size: 47324160,
    model_format: 'pt',
    creation_time: 1705324200, // 2024-01-15 14:30:00
    performance_metrics: {
      avg_reward: 847.3,
      success_rate: 0.942,
      std_reward: 23.1,
    },
  },
  anymal_c_flat: {
    model_name: 'anymal_c_flat_terrain',
    model_path: '/models/anymal_c_flat_terrain_v1.8.pt',
    file_size: 33685504,
    model_format: 'pt',
    creation_time: 1705065300, // 2024-01-12 09:15:00
    performance_metrics: {
      avg_reward: 723.1,
      success_rate: 0.887,
      std_reward: 31.7,
    },
  },
};

// Mock deployment job data
export const mockDeploymentJobs = [
  {
    key: '1',
    id: 'export_001',
    modelName: 'anymal_c_rough_terrain_v2.1',
    format: 'ONNX',
    platform: 'GPU',
    status: 'completed',
    progress: 100,
    size: '45.2 MB',
    createdAt: '2024-01-15 14:30:00',
    exportPath: '/exported/anymal_c_rough_terrain_v2.1.onnx',
  },
  {
    key: '2',
    id: 'export_002',
    modelName: 'anymal_c_flat_terrain_v1.8',
    format: 'JIT',
    platform: 'CPU',
    status: 'exporting',
    progress: 65,
    size: '32.1 MB',
    createdAt: '2024-01-15 15:45:00',
  },
  {
    key: '3',
    id: 'export_003',
    modelName: 'anymal_c_stairs_v1.2',
    format: 'TensorRT',
    platform: 'GPU',
    status: 'error',
    progress: 0,
    size: '0 MB',
    createdAt: '2024-01-15 16:20:00',
    error: 'TensorRT conversion failed: Unsupported layer type',
  },
];

// Mock validation result
export const mockValidationResult = {
  isValid: true,
  modelFormat: '.onnx',
  modelSizeMb: 45.2,
  inputShapes: {
    observation: [1, 48],
  },
  outputShapes: {
    action: [1, 12],
  },
  inferenceTimeMs: 2.34,
  validationErrors: [],
  compatibilityInfo: {
    onnx_version: '1.12.0',
    opset_version: 11,
  },
};

// Mock failed validation result
export const mockFailedValidationResult = {
  isValid: false,
  modelFormat: '.onnx',
  modelSizeMb: 45.2,
  inputShapes: {},
  outputShapes: {},
  inferenceTimeMs: 0,
  validationErrors: [
    'Invalid model format',
    'Missing required input tensors',
    'Incompatible ONNX version',
  ],
  compatibilityInfo: {},
};

// Mock deployment status messages
export const mockStatusMessages = {
  modelListResponse: {
    payload: {
      available_models: mockModelData,
      deployed_models: {
        anymal_c_rough: {
          ...mockModelData.anymal_c_rough,
          deployed: true,
          deployment_time: Date.now() / 1000,
        },
      },
    },
  },
  jobStatusUpdate: {
    payload: {
      job_id: 'export_123',
      status: 'exporting',
      progress: 45.0,
      message: 'Converting model to ONNX format',
      timestamp: Date.now() / 1000,
    },
  },
  jobCompleted: {
    payload: {
      job_id: 'export_123',
      status: 'completed',
      progress: 100.0,
      export_path: '/exported/model_123.onnx',
      message: 'Model export completed successfully',
      timestamp: Date.now() / 1000,
    },
  },
  jobFailed: {
    payload: {
      job_id: 'export_456',
      status: 'error',
      progress: 0,
      error_message: 'Export failed: Out of memory',
      message: 'Model export failed',
      timestamp: Date.now() / 1000,
    },
  },
  validationCompleted: {
    payload: {
      job_id: 'validation_789',
      validation_result: mockValidationResult,
      message: 'Model validation completed',
      timestamp: Date.now() / 1000,
    },
  },
};

// Test utilities for simulating user interactions
export const fillExportForm = (container: HTMLElement, formData: any) => {
  // Helper function to fill out the export form
  // This would be implemented with specific form field interactions
};

export const simulateFileUpload = (uploadComponent: HTMLElement, file: File) => {
  // Helper function to simulate file upload
  // This would be implemented with drag/drop or file input simulation
};

export const waitForLoadingToComplete = async (timeout = 5000) => {
  // Helper function to wait for async operations to complete
  return new Promise((resolve) => {
    setTimeout(resolve, 100); // Short delay for testing
  });
};

// Mock performance data
export const mockPerformanceData = [
  {
    key: '1',
    model: 'anymal_c_v2.1',
    format: 'ONNX',
    inferenceTime: 2.3,
    memory: 128,
    accuracy: '94.2%',
    size: 45.2,
  },
  {
    key: '2',
    model: 'anymal_c_v2.1',
    format: 'JIT',
    inferenceTime: 1.8,
    memory: 156,
    accuracy: '94.5%',
    size: 52.1,
  },
  {
    key: '3',
    model: 'anymal_c_v1.8',
    format: 'ONNX',
    inferenceTime: 3.1,
    memory: 112,
    accuracy: '92.1%',
    size: 38.7,
  },
];

// Mock version data
export const mockVersionData = [
  {
    key: '1',
    version: '2.1.3',
    model: 'anymal_c_rough_terrain',
    created: '2024-01-20 10:30:00',
    performance: 94.2,
    size: 45.2,
    isCurrent: true,
  },
  {
    key: '2',
    version: '2.1.2',
    model: 'anymal_c_rough_terrain',
    created: '2024-01-18 14:15:00',
    performance: 93.8,
    size: 44.8,
    isCurrent: false,
  },
  {
    key: '3',
    version: '2.1.1',
    model: 'anymal_c_rough_terrain',
    created: '2024-01-15 09:20:00',
    performance: 92.5,
    size: 43.1,
    isCurrent: false,
  },
];

// Test helpers for assertions
export const expectModelToBeDisplayed = (container: HTMLElement, modelName: string) => {
  // Helper to assert that a model is properly displayed
};

export const expectJobStatusToBe = (container: HTMLElement, jobId: string, expectedStatus: string) => {
  // Helper to assert job status
};

export const expectValidationResultsToShow = (container: HTMLElement, isValid: boolean) => {
  // Helper to assert validation results display
};

// Mock log entries
export const mockLogEntries = [
  {
    timestamp: Date.now() - 300000, // 5 minutes ago
    level: 'info' as const,
    message: 'Starting model export',
    details: { job_id: 'export_123', model_path: '/models/test.pt' },
  },
  {
    timestamp: Date.now() - 240000, // 4 minutes ago
    level: 'info' as const,
    message: 'Model loaded successfully',
    details: { model_size: '45.2 MB' },
  },
  {
    timestamp: Date.now() - 180000, // 3 minutes ago
    level: 'warning' as const,
    message: 'Optimization level set to aggressive',
    details: { optimization_level: 'aggressive' },
  },
  {
    timestamp: Date.now() - 60000, // 1 minute ago
    level: 'info' as const,
    message: 'Export completed successfully',
    details: { export_path: '/exported/test.onnx' },
  },
];

export default {
  createMockZenohClient,
  mockModelData,
  mockDeploymentJobs,
  mockValidationResult,
  mockFailedValidationResult,
  mockStatusMessages,
  mockPerformanceData,
  mockVersionData,
  mockLogEntries,
};