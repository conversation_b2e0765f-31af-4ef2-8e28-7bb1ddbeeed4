import React from 'react';
import { ConfigProvider, Layout, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import MainLayout from './components/layout/MainLayout';
import './App.css';

const { darkAlgorithm } = theme;

const App: React.FC = () => {
  return (
    <I18nextProvider i18n={i18n}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: darkAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 6,
          },
        }}
      >
        <Layout style={{ minHeight: '100vh' }}>
          <MainLayout />
        </Layout>
      </ConfigProvider>
    </I18nextProvider>
  );
};

export default App;
