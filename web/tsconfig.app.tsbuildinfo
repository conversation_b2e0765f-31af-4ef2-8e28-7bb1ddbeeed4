{"root": ["./src/App.tsx", "./src/main.tsx", "./src/components/dashboard/DashboardView.tsx", "./src/components/deployment/DeploymentView.tsx", "./src/components/deployment/__tests__/DeploymentView.integration.test.tsx", "./src/components/deployment/__tests__/DeploymentView.test.tsx", "./src/components/deployment/__tests__/test-utils.ts", "./src/components/layout/MainLayout.tsx", "./src/components/layout/SystemStatusBar.tsx", "./src/components/monitoring/ErrorMonitoringView.tsx", "./src/components/monitoring/MonitoringView.tsx", "./src/components/monitoring/ResourceMonitor.tsx", "./src/components/monitoring/__tests__/MonitoringView.integration.test.tsx", "./src/components/monitoring/__tests__/MonitoringView.test.tsx", "./src/components/monitoring/__tests__/ResourceMonitor.minimal.test.tsx", "./src/components/monitoring/__tests__/ResourceMonitor.test.tsx", "./src/components/monitoring/__tests__/setup.test.tsx", "./src/components/monitoring/__tests__/test-utils.ts", "./src/components/simulation/RobotControlPanel.tsx", "./src/components/simulation/RobotStateDisplay.tsx", "./src/components/simulation/SimulationConfiguration.tsx", "./src/components/simulation/SimulationView.tsx", "./src/components/training/TrainingChart.tsx", "./src/components/training/TrainingComparison.tsx", "./src/components/training/TrainingView.tsx", "./src/hooks/useZenohInitialization.ts", "./src/i18n/index.ts", "./src/locales/zh-CN.ts", "./src/services/zenoh-client.ts", "./src/stores/zenoh-store.ts", "./src/types/zenoh-types.ts"], "version": "5.8.3"}