#!/usr/bin/env python3
"""
WebSocket connection test for EngineAI Legged Gym
"""

import asyncio
import json
import websockets
import sys

async def test_websocket_connection():
    """Test WebSocket connection to the backend"""
    uri = "ws://localhost:8080"

    try:
        print(f"🔗 Connecting to {uri}...")

        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established!")

            # Test sending a ping message
            ping_message = {
                "type": "ping",
                "timestamp": asyncio.get_event_loop().time()
            }

            await websocket.send(json.dumps(ping_message))
            print("📤 Sent ping message")

            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Received response: {response}")

                # Try to parse as JSON
                try:
                    data = json.loads(response)
                    print(f"✅ Valid JSON response: {data}")
                except json.JSONDecodeError:
                    print(f"⚠️  Non-JSON response: {response}")

            except asyncio.TimeoutError:
                print("⚠️  No response received within 5 seconds")

            # Test subscription
            subscribe_message = {
                "type": "subscribe",
                "topic": "legged_gym/system/status"
            }

            await websocket.send(json.dumps(subscribe_message))
            print("📤 Sent subscription request")

            # Wait for subscription confirmation or data
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Subscription response: {response}")
            except asyncio.TimeoutError:
                print("⚠️  No subscription response within 5 seconds")

            print("✅ WebSocket test completed successfully!")
            return True

    except ConnectionRefusedError:
        print("❌ Connection refused - WebSocket server not running")
        return False
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("=" * 60)
    print("WebSocket Connection Test")
    print("=" * 60)

    success = await test_websocket_connection()

    print("\n" + "=" * 60)
    if success:
        print("🎉 WebSocket connection test passed!")
    else:
        print("❌ WebSocket connection test failed!")
    print("=" * 60)

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))