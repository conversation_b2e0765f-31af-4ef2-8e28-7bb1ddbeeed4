version: '3.8'

services:
  engineai-legged-gym:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: engineai-legged-gym
    ports:
      - "3000:3000"    # Web interface
      - "8080:8080"    # WebSocket API
      - "7447:7447"    # Zenoh router
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
      - ./models:/app/models
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - ZENOH_LOG_LEVEL=info
      - PYTHONPATH=/app
      - NODE_ENV=production
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Separate Zenoh router service
  zenoh-router:
    image: eclipse/zenoh:latest
    container_name: zenoh-router
    ports:
      - "7447:7447"
    command: ["zenohd", "-c", "/zenoh-config.json5"]
    volumes:
      - ./zenoh_services/config/zenoh-router.json5:/zenoh-config.json5:ro
    restart: unless-stopped
    profiles:
      - router

  # Optional: Monitoring with Prometheus and Grafana
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  grafana-storage:

networks:
  default:
    name: engineai-network
