#!/usr/bin/env python3
"""
Service entry points for EngineAI Zenoh services
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

logger = logging.getLogger(__name__)

async def run_training_service():
    """Run training service"""
    try:
        from src.zenoh_services.services.training_service import TrainingService, TrainingServiceConfig
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        
        # Create configuration
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )
        
        training_config = TrainingServiceConfig(
            task_name="anymal_c_flat",
            num_envs=4096,
            max_iterations=1500
        )
        
        # Create and start service
        session_manager = EnhancedZenohSessionManager(zenoh_config, "training_service")
        service = TrainingService(session_manager, training_config)
        
        await service.initialize()
        await service.start()
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Training service interrupted")
    except Exception as e:
        logger.error(f"Training service error: {e}")
        raise

async def run_simulation_service():
    """Run simulation service"""
    try:
        from src.zenoh_services.services.simulation_service import SimulationService
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        
        # Create configuration
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )
        
        # Create and start service
        session_manager = EnhancedZenohSessionManager(zenoh_config, "simulation_service")
        service = SimulationService(session_manager)
        
        await service.initialize()
        await service.start()
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Simulation service interrupted")
    except Exception as e:
        logger.error(f"Simulation service error: {e}")
        raise

async def run_deployment_service():
    """Run deployment service"""
    try:
        from src.zenoh_services.services.deployment_service import DeploymentService, DeploymentServiceConfig
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        
        # Create configuration
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )
        
        deployment_config = DeploymentServiceConfig()
        
        # Create and start service
        session_manager = EnhancedZenohSessionManager(zenoh_config, "deployment_service")
        service = DeploymentService(session_manager, deployment_config)
        
        await service.initialize()
        await service.start()
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Deployment service interrupted")
    except Exception as e:
        logger.error(f"Deployment service error: {e}")
        raise

async def run_config_service():
    """Run config service"""
    try:
        from src.zenoh_services.services.config_service import ConfigService
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        
        # Create configuration
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )
        
        # Create and start service
        session_manager = EnhancedZenohSessionManager(zenoh_config, "config_service")
        service = ConfigService(session_manager)
        
        await service.initialize()
        await service.start()
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Config service interrupted")
    except Exception as e:
        logger.error(f"Config service error: {e}")
        raise

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print("Usage: python -m services <service_name>")
        print("Available services: training_service, simulation_service, deployment_service, config_service")
        return 1

    service_name = sys.argv[1]

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        if service_name == "training_service":
            print(f"Starting {service_name}...")
            asyncio.run(run_training_service())
        elif service_name == "simulation_service":
            print(f"Starting {service_name}...")
            asyncio.run(run_simulation_service())
        elif service_name == "deployment_service":
            print(f"Starting {service_name}...")
            asyncio.run(run_deployment_service())
        elif service_name == "config_service":
            print(f"Starting {service_name}...")
            asyncio.run(run_config_service())
        else:
            print(f"Unknown service: {service_name}")
            return 1
    except Exception as e:
        print(f"Error starting {service_name}: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
