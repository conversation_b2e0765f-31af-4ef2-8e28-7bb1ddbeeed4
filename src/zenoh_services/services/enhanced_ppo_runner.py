"""
Enhanced PPO Runner with Zenoh integration for real-time metrics publishing
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, Callable
import torch
import numpy as np
from collections import deque

# Import existing PPO runner
from rsl_rl.runners.on_policy_runner import OnPolicyRunner
from rsl_rl.algorithms import PPO
from rsl_rl.env import VecEnv

# Import Zenoh components
from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from src.zenoh_services.core.topic_manager import TopicManager
from src.zenoh_services.core.data_models import TrainingMetrics
from src.zenoh_services.core.message_format import MessageFactory, Priority
from src.zenoh_services.core import topics

logger = logging.getLogger(__name__)


class ZenohIntegratedPPORunner(OnPolicyRunner):
    """
    Enhanced PPO Runner that publishes real-time training metrics to Zenoh
    """
    
    def __init__(self, 
                 env: VecEnv,
                 train_cfg,
                 log_dir=None,
                 device='cpu',
                 session_manager: Optional[EnhancedZenohSessionManager] = None):
        
        # Initialize parent class
        super().__init__(env, train_cfg, log_dir, device)
        
        # Zenoh integration
        self.session_manager = session_manager
        self.topic_manager = None
        self.zenoh_enabled = session_manager is not None
        
        if self.zenoh_enabled:
            self.topic_manager = TopicManager(session_manager)
            
        # Metrics tracking
        self.metrics_publish_interval = 1.0  # seconds
        self.last_metrics_publish = 0.0
        self.iteration_start_time = 0.0
        
        # Training state
        self.current_mean_reward = 0.0
        self.current_mean_episode_length = 0.0
        self.current_learning_rate = 0.0
        self.current_entropy = 0.0
        self.current_kl_div = 0.0
        self.current_policy_loss = 0.0
        self.current_value_loss = 0.0
        
    async def initialize_zenoh_publishers(self):
        """Initialize Zenoh publishers for metrics"""
        if not self.zenoh_enabled:
            return
            
        try:
            await self.topic_manager.register_topic(topics.TRAINING_METRICS)
            await self.topic_manager.create_publisher(topics.TRAINING_METRICS)
            logger.info("Zenoh publishers initialized for training metrics")
        except Exception as e:
            logger.error(f"Failed to initialize Zenoh publishers: {e}")
            self.zenoh_enabled = False
    
    def learn(self, num_learning_iterations, init_at_random_ep_len=False):
        """
        Enhanced learn method with real-time metrics publishing
        """
        # Initialize Zenoh if available
        if self.zenoh_enabled:
            asyncio.create_task(self.initialize_zenoh_publishers())
        
        # Call parent learn method with our custom iteration hook
        return self._learn_with_metrics(num_learning_iterations, init_at_random_ep_len)
    
    def _learn_with_metrics(self, num_learning_iterations, init_at_random_ep_len=False):
        """
        Modified learn method that publishes metrics during training
        """
        # Initialize writer
        if self.log_dir is not None and self.writer is None:
            from torch.utils.tensorboard import SummaryWriter
            self.writer = SummaryWriter(log_dir=self.log_dir, flush_secs=10)
            
        if init_at_random_ep_len:
            self.env.episode_length_buf = torch.randint_like(
                self.env.episode_length_buf, 
                high=int(self.env.max_episode_length)
            )
            
        obs = self.env.get_observations()
        privileged_obs = self.env.get_privileged_observations()
        critic_obs = privileged_obs if privileged_obs is not None else obs
        obs, critic_obs = obs.to(self.device), critic_obs.to(self.device)
        self.alg.actor_critic.train()  # switch to train mode
        
        ep_infos = []
        rewbuffer = deque(maxlen=100)
        lenbuffer = deque(maxlen=100)
        cur_reward_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_episode_length = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        
        tot_iter = self.current_learning_iteration + num_learning_iterations
        
        for it in range(self.current_learning_iteration, tot_iter):
            self.iteration_start_time = time.time()
            start = time.time()
            
            # Rollout
            with torch.inference_mode():
                for i in range(self.num_steps_per_env):
                    actions = self.alg.act(obs, critic_obs)
                    obs, privileged_obs, rewards, dones, infos = self.env.step(actions)
                    critic_obs = privileged_obs if privileged_obs is not None else obs
                    obs, critic_obs, rewards, dones = obs.to(self.device), critic_obs.to(self.device), rewards.to(self.device), dones.to(self.device)
                    self.alg.process_env_step(rewards, dones, infos)
                    
                    if self.log_dir is not None:
                        # Book keeping
                        if 'episode' in infos:
                            ep_infos.append(infos['episode'])
                        cur_reward_sum += rewards
                        cur_episode_length += 1
                        new_ids = (dones > 0).nonzero(as_tuple=False)
                        rewbuffer.extend(cur_reward_sum[new_ids][:, 0].cpu().numpy().tolist())
                        lenbuffer.extend(cur_episode_length[new_ids][:, 0].cpu().numpy().tolist())
                        cur_reward_sum[new_ids] = 0
                        cur_episode_length[new_ids] = 0
                
                stop = time.time()
                collection_time = stop - start
                
                # Learning step
                start = stop
                self.alg.compute_returns(critic_obs)
            
            mean_value_loss, mean_surrogate_loss, mean_entropy_loss = self.alg.update()
            stop = time.time()
            learn_time = stop - start
            
            # Update current metrics
            if len(rewbuffer) > 0:
                self.current_mean_reward = np.mean(rewbuffer)
                self.current_mean_episode_length = np.mean(lenbuffer)
            
            # Get learning rate from optimizer
            if hasattr(self.alg, 'optimizer'):
                self.current_learning_rate = self.alg.optimizer.param_groups[0]['lr']
            
            # Store loss values
            self.current_policy_loss = mean_surrogate_loss
            self.current_value_loss = mean_value_loss
            self.current_entropy = mean_entropy_loss
            
            # Publish metrics to Zenoh
            if self.zenoh_enabled:
                asyncio.create_task(self._publish_metrics_async(it))
            
            # Logging to tensorboard
            if self.log_dir is not None:
                self.tot_timesteps += self.num_steps_per_env * self.env.num_envs
                self.tot_time += collection_time + learn_time
                self.current_learning_iteration = it
                
                # Log to tensorboard
                if self.writer is not None:
                    self.writer.add_scalar('Loss/value_function', mean_value_loss, it)
                    self.writer.add_scalar('Loss/surrogate', mean_surrogate_loss, it)
                    self.writer.add_scalar('Loss/entropy', mean_entropy_loss, it)
                    self.writer.add_scalar('Policy/learning_rate', self.current_learning_rate, it)
                    
                    if len(rewbuffer) > 0:
                        self.writer.add_scalar('Train/mean_reward', self.current_mean_reward, it)
                        self.writer.add_scalar('Train/mean_episode_length', self.current_mean_episode_length, it)
                
                # Print progress
                if it % self.save_interval == 0:
                    import os
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
                
                ep_string = f''
                if len(rewbuffer) > 0:
                    ep_string = f"| Mean reward: {self.current_mean_reward:.2f} | Mean episode length: {self.current_mean_episode_length:.2f}"
                
                logger.info(f"Iter: {it} | FPS: {int(self.num_steps_per_env * self.env.num_envs / (collection_time + learn_time))} {ep_string}")
        
        self.current_learning_iteration = tot_iter
        import os
        self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(self.current_learning_iteration)))
    
    async def _publish_metrics_async(self, iteration: int):
        """Publish training metrics to Zenoh asynchronously"""
        try:
            current_time = time.time()
            if current_time - self.last_metrics_publish < self.metrics_publish_interval:
                return
            
            # Create metrics object
            metrics = TrainingMetrics(
                iteration=iteration,
                mean_reward=float(self.current_mean_reward),
                std_reward=0.0,  # Could calculate if needed
                mean_episode_length=float(self.current_mean_episode_length),
                learning_rate=float(self.current_learning_rate),
                entropy=float(self.current_entropy),
                kl_divergence=0.0,  # Could add KL divergence tracking
                policy_loss=float(self.current_policy_loss),
                value_loss=float(self.current_value_loss),
                timestamp=current_time
            )
            
            # Create and publish message
            message = MessageFactory.create_data(metrics.to_dict(), "enhanced_ppo_runner")
            message.header.priority = Priority.NORMAL
            
            await self.topic_manager.publish_message(topics.TRAINING_METRICS, message)
            
            self.last_metrics_publish = current_time
            logger.debug(f"Published training metrics for iteration {iteration}")
            
        except Exception as e:
            logger.error(f"Failed to publish training metrics: {e}")
    
    def get_inference_policy(self, device=None):
        """Get inference policy for testing"""
        if device is None:
            device = self.device
        self.alg.actor_critic.eval()
        return self.alg.actor_critic.act_inference


class MetricsCollector:
    """
    Utility class for collecting and aggregating training metrics
    """
    
    def __init__(self, buffer_size: int = 100):
        self.buffer_size = buffer_size
        self.reward_buffer = deque(maxlen=buffer_size)
        self.length_buffer = deque(maxlen=buffer_size)
        self.reset()
    
    def reset(self):
        """Reset all buffers"""
        self.reward_buffer.clear()
        self.length_buffer.clear()
    
    def add_episode(self, reward: float, length: int):
        """Add episode data"""
        self.reward_buffer.append(reward)
        self.length_buffer.append(length)
    
    def get_stats(self) -> Dict[str, float]:
        """Get current statistics"""
        if len(self.reward_buffer) == 0:
            return {
                'mean_reward': 0.0,
                'std_reward': 0.0,
                'mean_episode_length': 0.0,
                'episode_count': 0
            }
        
        rewards = np.array(self.reward_buffer)
        lengths = np.array(self.length_buffer)
        
        return {
            'mean_reward': float(np.mean(rewards)),
            'std_reward': float(np.std(rewards)),
            'mean_episode_length': float(np.mean(lengths)),
            'episode_count': len(rewards)
        }


# Factory function to create enhanced runner
def create_enhanced_ppo_runner(
    env: VecEnv,
    train_cfg,
    log_dir=None,
    device='cpu',
    session_manager: Optional[EnhancedZenohSessionManager] = None
) -> ZenohIntegratedPPORunner:
    """Create an enhanced PPO runner with Zenoh integration"""
    return ZenohIntegratedPPORunner(env, train_cfg, log_dir, device, session_manager)