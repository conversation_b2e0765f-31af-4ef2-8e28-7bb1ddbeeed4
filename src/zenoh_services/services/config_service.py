"""
Configuration Management Service for Legged Robot Gymnasium
Centralized configuration management with versioning, validation, and conflict resolution
"""

import asyncio
import logging
import time
import os
import json
import hashlib
import shutil
from typing import Optional, Dict, Any, List, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path
import copy

from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from src.zenoh_services.core.topic_manager import TopicManager
from src.zenoh_services.core.data_models import (
    TrainingConfig, PlayConfig, TerrainConfig, PhysicsConfig, RobotConfig,
    SimulationConfig, DeploymentConfig, SerializationMixin,
    ConfigurationRequest, ConfigurationResponse
)
from src.zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from src.zenoh_services.core import topics

logger = logging.getLogger(__name__)


class ConfigServiceState(Enum):
    """Configuration service states"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    SYNCHRONIZING = "synchronizing"
    VALIDATING = "validating"
    MERGING = "merging"
    PERSISTING = "persisting"
    ERROR = "error"


class ConfigurationStatus(Enum):
    """Configuration status enumeration"""
    DRAFT = "draft"
    VALIDATED = "validated"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    CONFLICTED = "conflicted"
    ARCHIVED = "archived"


class ConflictResolutionStrategy(Enum):
    """Configuration conflict resolution strategies"""
    REJECT = "reject"  # Reject conflicting changes
    OVERWRITE = "overwrite"  # Overwrite with new configuration
    MERGE = "merge"  # Attempt intelligent merge
    PROMPT = "prompt"  # Request user resolution
    VERSION_BRANCH = "version_branch"  # Create new version branch


@dataclass
class ConfigServiceConfig:
    """Configuration for the configuration management service"""
    # Storage paths
    config_storage_path: str = "./system_configs"
    version_storage_path: str = "./config_versions"
    backup_path: str = "./config_backups"
    template_path: str = "./config_templates"
    
    # Version control
    max_versions_per_config: int = 50
    auto_cleanup_days: int = 30
    enable_compression: bool = True
    
    # Validation
    strict_validation: bool = True
    enable_schema_validation: bool = True
    validate_cross_dependencies: bool = True
    
    # Conflict resolution
    default_conflict_strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.MERGE
    allow_force_overwrite: bool = True
    max_merge_attempts: int = 3
    
    # Performance
    cache_size: int = 100
    sync_interval: float = 5.0
    batch_update_size: int = 10
    
    # Backup and recovery
    auto_backup_enabled: bool = True
    backup_interval_hours: int = 6
    max_backups: int = 24


@dataclass
class ConfigurationVersion(SerializationMixin):
    """Configuration version metadata"""
    version_id: str
    config_type: str
    config_name: str
    version_number: int
    parent_version: Optional[str] = None
    branch_name: str = "main"
    
    # Metadata
    author: str = "system"
    comment: str = ""
    created_at: float = field(default_factory=time.time)
    modified_at: float = field(default_factory=time.time)
    
    # Status
    status: ConfigurationStatus = ConfigurationStatus.DRAFT
    is_active: bool = False
    tags: List[str] = field(default_factory=list)
    
    # Content metadata
    config_hash: str = ""
    file_size: int = 0
    
    # Validation
    validation_status: str = "pending"
    validation_errors: List[str] = field(default_factory=list)
    
    def validate(self) -> bool:
        """Validate version metadata"""
        return (
            len(self.version_id) > 0 and
            len(self.config_type) > 0 and
            len(self.config_name) > 0 and
            self.version_number > 0 and
            self.created_at > 0
        )


@dataclass
class ConfigurationConflict(SerializationMixin):
    """Configuration conflict information"""
    conflict_id: str
    config_type: str
    config_name: str
    
    # Conflicting versions
    current_version: str
    incoming_version: str
    base_version: Optional[str] = None
    
    # Conflict details
    conflicted_fields: List[str] = field(default_factory=list)
    conflict_severity: str = "medium"  # low, medium, high, critical
    
    # Resolution
    resolution_strategy: Optional[ConflictResolutionStrategy] = None
    resolution_status: str = "pending"  # pending, resolved, failed
    
    # Timestamps
    detected_at: float = field(default_factory=time.time)
    resolved_at: Optional[float] = None
    
    def validate(self) -> bool:
        """Validate conflict information"""
        return (
            len(self.conflict_id) > 0 and
            len(self.current_version) > 0 and
            len(self.incoming_version) > 0
        )


@dataclass
class ConfigurationSnapshot(SerializationMixin):
    """Complete configuration snapshot"""
    snapshot_id: str
    snapshot_name: str
    created_at: float = field(default_factory=time.time)
    
    # Configuration versions included
    training_config_version: Optional[str] = None
    simulation_config_version: Optional[str] = None
    deployment_config_version: Optional[str] = None
    
    # Snapshot metadata
    description: str = ""
    tags: List[str] = field(default_factory=list)
    is_baseline: bool = False
    
    # Validation status
    is_validated: bool = False
    validation_errors: List[str] = field(default_factory=list)
    
    def validate(self) -> bool:
        """Validate snapshot"""
        return len(self.snapshot_id) > 0 and len(self.snapshot_name) > 0


class ConfigurationService:
    """
    Centralized configuration management service with versioning, validation, and conflict resolution
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager, config: ConfigServiceConfig = None):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.config = config or ConfigServiceConfig()
        
        # Service state
        self.state = ConfigServiceState.IDLE
        self.is_initialized = False
        
        # Configuration storage
        self.configurations: Dict[str, Dict[str, Any]] = {}  # {config_type: {config_name: config_data}}
        self.versions: Dict[str, List[ConfigurationVersion]] = {}  # {config_key: [versions]}
        self.active_versions: Dict[str, str] = {}  # {config_key: active_version_id}
        
        # Conflict management
        self.pending_conflicts: Dict[str, ConfigurationConflict] = {}
        self.resolved_conflicts: List[ConfigurationConflict] = []
        
        # Snapshots
        self.snapshots: Dict[str, ConfigurationSnapshot] = {}
        
        # Templates
        self.templates: Dict[str, Dict[str, Any]] = {}
        
        # Cache and performance
        self.config_cache: Dict[str, Tuple[Any, float]] = {}
        self.cache_ttl = 300.0  # 5 minutes
        
        # Background tasks
        self.sync_task: Optional[asyncio.Task] = None
        self.backup_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Locks
        self.config_lock = asyncio.RLock()
        self.version_lock = asyncio.RLock()
        self.backup_lock = asyncio.Lock()
    
    async def initialize(self) -> bool:
        """Initialize the configuration service"""
        try:
            self.state = ConfigServiceState.INITIALIZING
            logger.info("Initializing configuration management service...")
            
            # Create storage directories
            self._create_storage_directories()
            
            # Load existing configurations
            await self._load_existing_configurations()
            
            # Load templates
            await self._load_configuration_templates()
            
            # Register topics
            await self._register_topics()
            
            # Setup command subscribers
            await self._setup_command_subscribers()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.is_initialized = True
            self.state = ConfigServiceState.IDLE
            logger.info("Configuration management service initialized successfully")
            return True
            
        except Exception as e:
            self.state = ConfigServiceState.ERROR
            logger.error(f"Failed to initialize configuration service: {e}")
            return False
    
    def _create_storage_directories(self):
        """Create necessary storage directories"""
        directories = [
            self.config.config_storage_path,
            self.config.version_storage_path,
            self.config.backup_path,
            self.config.template_path
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        # Create subdirectories for different config types
        config_types = ["training", "simulation", "deployment", "system"]
        for config_type in config_types:
            for base_dir in directories:
                type_dir = os.path.join(base_dir, config_type)
                os.makedirs(type_dir, exist_ok=True)
    
    async def _load_existing_configurations(self):
        """Load existing configurations from storage"""
        try:
            config_dir = Path(self.config.config_storage_path)
            
            for config_type_dir in config_dir.iterdir():
                if config_type_dir.is_dir():
                    config_type = config_type_dir.name
                    
                    for config_file in config_type_dir.glob("*.json"):
                        config_name = config_file.stem
                        
                        try:
                            with open(config_file, 'r', encoding='utf-8') as f:
                                config_data = json.load(f)
                            
                            config_key = f"{config_type}/{config_name}"
                            
                            if config_type not in self.configurations:
                                self.configurations[config_type] = {}
                            
                            self.configurations[config_type][config_name] = config_data
                            
                            # Load version history
                            await self._load_version_history(config_key)
                            
                        except Exception as e:
                            logger.warning(f"Failed to load configuration {config_file}: {e}")
            
            logger.info(f"Loaded {sum(len(configs) for configs in self.configurations.values())} configurations")
            
        except Exception as e:
            logger.error(f"Error loading existing configurations: {e}")
    
    async def _load_version_history(self, config_key: str):
        """Load version history for a configuration"""
        try:
            versions_dir = Path(self.config.version_storage_path) / config_key.replace("/", "_")
            
            if not versions_dir.exists():
                return
            
            versions = []
            
            for version_file in versions_dir.glob("version_*.json"):
                try:
                    with open(version_file, 'r', encoding='utf-8') as f:
                        version_data = json.load(f)
                    
                    version = ConfigurationVersion.from_dict(version_data)
                    versions.append(version)
                    
                    # Set active version
                    if version.is_active:
                        self.active_versions[config_key] = version.version_id
                        
                except Exception as e:
                    logger.warning(f"Failed to load version {version_file}: {e}")
            
            # Sort versions by creation time
            versions.sort(key=lambda v: v.created_at)
            
            self.versions[config_key] = versions
            
        except Exception as e:
            logger.error(f"Error loading version history for {config_key}: {e}")
    
    async def _load_configuration_templates(self):
        """Load configuration templates"""
        try:
            template_dir = Path(self.config.template_path)
            
            if not template_dir.exists():
                await self._create_default_templates()
                return
            
            for template_file in template_dir.glob("*.json"):
                try:
                    template_name = template_file.stem
                    
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                    
                    self.templates[template_name] = template_data
                    
                except Exception as e:
                    logger.warning(f"Failed to load template {template_file}: {e}")
            
            logger.info(f"Loaded {len(self.templates)} configuration templates")
            
        except Exception as e:
            logger.error(f"Error loading configuration templates: {e}")
    
    async def _create_default_templates(self):
        """Create default configuration templates"""
        try:
            # Default training configuration template
            training_template = {
                "name": "default_training_template",
                "description": "Default training configuration template",
                "config": {
                    "task_name": "${TASK_NAME}",
                    "num_envs": 4096,
                    "max_iterations": 1500,
                    "learning_rate": 3e-4,
                    "batch_size": 24576,
                    "experiment_name": "${EXPERIMENT_NAME}",
                    "run_name": "${RUN_NAME}",
                    "resume": False
                },
                "variables": {
                    "TASK_NAME": "anymal_c_flat",
                    "EXPERIMENT_NAME": "default_experiment", 
                    "RUN_NAME": "run_001"
                }
            }
            
            # Default simulation configuration template
            simulation_template = {
                "name": "default_simulation_template",
                "description": "Default simulation configuration template",
                "config": {
                    "terrain": {
                        "mesh_type": "plane",
                        "terrain_length": 8.0,
                        "terrain_width": 8.0,
                        "horizontal_scale": 0.1,
                        "vertical_scale": 0.005
                    },
                    "physics": {
                        "dt": 0.005,
                        "gravity": [0.0, 0.0, -9.81],
                        "solver_type": "PGS",
                        "num_position_iterations": 4,
                        "num_velocity_iterations": 1
                    },
                    "robot": {
                        "name": "anymal_c",
                        "base_mass": 35.0,
                        "joint_stiffness": 80.0,
                        "joint_damping": 2.0,
                        "control_frequency": 50.0
                    },
                    "num_envs": 4096,
                    "episode_length": 1000
                }
            }
            
            # Default deployment configuration template
            deployment_template = {
                "name": "default_deployment_template", 
                "description": "Default deployment configuration template",
                "config": {
                    "export_format": "jit",
                    "target_platform": "cpu",
                    "optimization_level": "default",
                    "model_path": "${MODEL_PATH}"
                },
                "variables": {
                    "MODEL_PATH": "/path/to/trained/model.pt"
                }
            }
            
            # Save templates
            templates = {
                "training_default": training_template,
                "simulation_default": simulation_template,
                "deployment_default": deployment_template
            }
            
            for template_name, template_data in templates.items():
                template_file = Path(self.config.template_path) / f"{template_name}.json"
                
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2)
                
                self.templates[template_name] = template_data
            
            logger.info("Created default configuration templates")
            
        except Exception as e:
            logger.error(f"Failed to create default templates: {e}")
    
    async def _register_topics(self):
        """Register all configuration-related topics"""
        topics_to_register = [
            topics.CONFIG_UPDATE,
            topics.CONFIG_REQUEST,
            "legged_gym/config/version",
            "legged_gym/config/conflict",
            "legged_gym/config/snapshot",
            "legged_gym/config/status"
        ]
        
        for topic in topics_to_register:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_command_subscribers(self):
        """Setup subscribers for configuration commands"""
        await self.topic_manager.create_subscriber(
            topics.CONFIG_REQUEST,
            self._handle_configuration_request
        )
    
    async def _handle_configuration_request(self, message):
        """Handle configuration management requests"""
        try:
            if hasattr(message, 'payload'):
                request_data = message.payload
            else:
                request_data = message
            
            request = ConfigurationRequest.from_dict(request_data)
            
            if request.action == "get":
                await self._handle_get_configuration(request)
            elif request.action == "set":
                await self._handle_set_configuration(request)
            elif request.action == "validate":
                await self._handle_validate_configuration(request)
            elif request.action == "list":
                await self._handle_list_configurations(request)
            elif request.action == "create_version":
                await self._handle_create_version(request)
            elif request.action == "get_versions":
                await self._handle_get_versions(request)
            elif request.action == "activate_version":
                await self._handle_activate_version(request)
            elif request.action == "create_snapshot":
                await self._handle_create_snapshot(request)
            elif request.action == "resolve_conflict":
                await self._handle_resolve_conflict(request)
            elif request.action == "apply_template":
                await self._handle_apply_template(request)
            
        except Exception as e:
            logger.error(f"Error handling configuration request: {e}")
            await self._send_configuration_response(False, str(e))
    
    async def _handle_get_configuration(self, request: ConfigurationRequest):
        """Handle get configuration request"""
        try:
            config_type = request.config_data.get("config_type")
            config_name = request.config_data.get("config_name", "default")
            version_id = request.config_data.get("version_id")
            
            if not config_type:
                raise ValueError("config_type is required")
            
            # Get configuration
            config_data = await self.get_configuration(config_type, config_name, version_id)
            
            response_data = {
                "config_type": config_type,
                "config_name": config_name,
                "config_data": config_data,
                "version_id": version_id or self.active_versions.get(f"{config_type}/{config_name}")
            }
            
            await self._send_configuration_response(True, "Configuration retrieved", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_set_configuration(self, request: ConfigurationRequest):
        """Handle set configuration request"""
        try:
            config_type = request.config_data.get("config_type")
            config_name = request.config_data.get("config_name", "default")
            config_data = request.config_data.get("config_data")
            author = request.config_data.get("author", "system")
            comment = request.config_data.get("comment", "")
            create_version = request.config_data.get("create_version", True)
            
            if not config_type or not config_data:
                raise ValueError("config_type and config_data are required")
            
            # Set configuration
            version_id = await self.set_configuration(
                config_type, config_name, config_data, 
                author=author, comment=comment, create_version=create_version
            )
            
            response_data = {
                "config_type": config_type,
                "config_name": config_name,
                "version_id": version_id
            }
            
            await self._send_configuration_response(True, "Configuration updated", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def get_configuration(self, config_type: str, config_name: str = "default", 
                               version_id: Optional[str] = None) -> Dict[str, Any]:
        """Get configuration with optional version"""
        try:
            async with self.config_lock:
                config_key = f"{config_type}/{config_name}"
                
                # Check cache first
                cache_key = f"{config_key}:{version_id or 'active'}"
                if cache_key in self.config_cache:
                    cached_data, cached_time = self.config_cache[cache_key]
                    if time.time() - cached_time < self.cache_ttl:
                        return cached_data
                
                if version_id:
                    # Get specific version
                    config_data = await self._load_configuration_version(config_key, version_id)
                else:
                    # Get active version
                    if config_type in self.configurations and config_name in self.configurations[config_type]:
                        config_data = self.configurations[config_type][config_name]
                    else:
                        raise ValueError(f"Configuration {config_key} not found")
                
                # Cache the result
                self.config_cache[cache_key] = (config_data, time.time())
                
                return config_data
                
        except Exception as e:
            logger.error(f"Error getting configuration {config_type}/{config_name}: {e}")
            raise
    
    async def set_configuration(self, config_type: str, config_name: str, config_data: Dict[str, Any],
                               author: str = "system", comment: str = "", 
                               create_version: bool = True) -> Optional[str]:
        """Set configuration with automatic versioning and conflict detection"""
        try:
            self.state = ConfigServiceState.VALIDATING
            
            async with self.config_lock:
                config_key = f"{config_type}/{config_name}"
                
                # Validate configuration
                if self.config.strict_validation:
                    validation_result = await self._validate_configuration(config_type, config_data)
                    if not validation_result["is_valid"]:
                        raise ValueError(f"Configuration validation failed: {validation_result['errors']}")
                
                # Check for conflicts
                current_config = self.configurations.get(config_type, {}).get(config_name)
                if current_config and current_config != config_data:
                    conflict = await self._detect_configuration_conflict(
                        config_key, current_config, config_data
                    )
                    
                    if conflict and conflict.conflict_severity in ["high", "critical"]:
                        # Handle conflict based on strategy
                        await self._handle_configuration_conflict(conflict, config_data)
                        return None  # Configuration not updated due to conflict
                
                # Update configuration
                if config_type not in self.configurations:
                    self.configurations[config_type] = {}
                
                self.configurations[config_type][config_name] = config_data
                
                # Create version if requested
                version_id = None
                if create_version:
                    version_id = await self._create_configuration_version(
                        config_key, config_data, author, comment
                    )
                
                # Persist configuration
                await self._persist_configuration(config_type, config_name, config_data)
                
                # Clear cache
                self._clear_config_cache(config_key)
                
                # Publish update notification
                await self._publish_configuration_update(config_type, config_name, config_data, version_id)
                
                self.state = ConfigServiceState.IDLE
                return version_id
                
        except Exception as e:
            self.state = ConfigServiceState.ERROR
            logger.error(f"Error setting configuration {config_type}/{config_name}: {e}")
            raise
    
    async def _validate_configuration(self, config_type: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate configuration data"""
        try:
            errors = []
            is_valid = True
            
            # Type-specific validation
            if config_type == "training":
                try:
                    training_config = TrainingConfig.from_dict(config_data)
                    if not training_config.validate():
                        errors.append("Training configuration validation failed")
                        is_valid = False
                except Exception as e:
                    errors.append(f"Training configuration error: {e}")
                    is_valid = False
            
            elif config_type == "simulation":
                try:
                    simulation_config = SimulationConfig.from_dict(config_data)
                    if not simulation_config.validate():
                        errors.append("Simulation configuration validation failed")
                        is_valid = False
                except Exception as e:
                    errors.append(f"Simulation configuration error: {e}")
                    is_valid = False
            
            elif config_type == "deployment":
                try:
                    deployment_config = DeploymentConfig.from_dict(config_data)
                    if not deployment_config.validate():
                        errors.append("Deployment configuration validation failed")
                        is_valid = False
                except Exception as e:
                    errors.append(f"Deployment configuration error: {e}")
                    is_valid = False
            
            # Cross-dependency validation
            if self.config.validate_cross_dependencies:
                cross_validation_errors = await self._validate_cross_dependencies(config_type, config_data)
                errors.extend(cross_validation_errors)
                if cross_validation_errors:
                    is_valid = False
            
            return {
                "is_valid": is_valid,
                "errors": errors,
                "warnings": []
            }
            
        except Exception as e:
            return {
                "is_valid": False,
                "errors": [f"Validation error: {e}"],
                "warnings": []
            }
    
    async def _validate_cross_dependencies(self, config_type: str, config_data: Dict[str, Any]) -> List[str]:
        """Validate cross-dependencies between configurations"""
        errors = []
        
        try:
            # Example cross-dependency validations
            if config_type == "training":
                # Check if required simulation configuration exists
                task_name = config_data.get("task_name")
                if task_name:
                    sim_config = self.configurations.get("simulation", {}).get("default")
                    if not sim_config:
                        errors.append("Training configuration requires default simulation configuration")
            
            elif config_type == "deployment":
                # Check if model path exists and is accessible
                model_path = config_data.get("model_path")
                if model_path and not os.path.exists(model_path):
                    errors.append(f"Deployment model path does not exist: {model_path}")
            
        except Exception as e:
            errors.append(f"Cross-dependency validation error: {e}")
        
        return errors
    
    async def _detect_configuration_conflict(self, config_key: str, current_config: Dict[str, Any], 
                                           new_config: Dict[str, Any]) -> Optional[ConfigurationConflict]:
        """Detect configuration conflicts"""
        try:
            conflicted_fields = []
            conflict_severity = "low"
            
            # Deep comparison to find conflicted fields
            def compare_dicts(current: Dict, new: Dict, path: str = ""):
                nonlocal conflicted_fields, conflict_severity
                
                for key, new_value in new.items():
                    field_path = f"{path}.{key}" if path else key
                    
                    if key not in current:
                        # New field - not a conflict
                        continue
                    
                    current_value = current[key]
                    
                    if isinstance(new_value, dict) and isinstance(current_value, dict):
                        compare_dicts(current_value, new_value, field_path)
                    elif current_value != new_value:
                        conflicted_fields.append(field_path)
                        
                        # Determine conflict severity
                        critical_fields = ["task_name", "model_path", "export_format"]
                        high_fields = ["num_envs", "max_iterations", "dt", "gravity"]
                        
                        if key in critical_fields:
                            conflict_severity = "critical"
                        elif key in high_fields and conflict_severity not in ["critical"]:
                            conflict_severity = "high"
                        elif conflict_severity == "low":
                            conflict_severity = "medium"
            
            compare_dicts(current_config, new_config)
            
            if not conflicted_fields:
                return None
            
            # Create conflict object
            config_parts = config_key.split("/")
            conflict_id = f"conflict_{int(time.time())}_{hashlib.md5(config_key.encode()).hexdigest()[:8]}"
            
            conflict = ConfigurationConflict(
                conflict_id=conflict_id,
                config_type=config_parts[0],
                config_name=config_parts[1],
                current_version=self.active_versions.get(config_key, "unknown"),
                incoming_version=f"incoming_{int(time.time())}",
                conflicted_fields=conflicted_fields,
                conflict_severity=conflict_severity
            )
            
            return conflict
            
        except Exception as e:
            logger.error(f"Error detecting configuration conflict: {e}")
            return None
    
    async def _handle_configuration_conflict(self, conflict: ConfigurationConflict, new_config: Dict[str, Any]):
        """Handle configuration conflict based on resolution strategy"""
        try:
            strategy = self.config.default_conflict_strategy
            
            if strategy == ConflictResolutionStrategy.REJECT:
                self.pending_conflicts[conflict.conflict_id] = conflict
                raise ValueError(f"Configuration conflict detected and rejected: {conflict.conflict_id}")
            
            elif strategy == ConflictResolutionStrategy.OVERWRITE:
                logger.warning(f"Overwriting conflicted configuration: {conflict.conflict_id}")
                # Continue with the update
                
            elif strategy == ConflictResolutionStrategy.MERGE:
                merged_config = await self._attempt_configuration_merge(conflict, new_config)
                if merged_config:
                    logger.info(f"Successfully merged conflicted configuration: {conflict.conflict_id}")
                    # Update with merged config
                else:
                    self.pending_conflicts[conflict.conflict_id] = conflict
                    raise ValueError(f"Configuration merge failed: {conflict.conflict_id}")
            
            elif strategy == ConflictResolutionStrategy.PROMPT:
                self.pending_conflicts[conflict.conflict_id] = conflict
                await self._request_conflict_resolution(conflict)
                raise ValueError(f"Configuration conflict requires user resolution: {conflict.conflict_id}")
            
            elif strategy == ConflictResolutionStrategy.VERSION_BRANCH:
                # Create a new version branch
                await self._create_version_branch(conflict, new_config)
                logger.info(f"Created version branch for conflict: {conflict.conflict_id}")
            
        except Exception as e:
            logger.error(f"Error handling configuration conflict: {e}")
            raise
    
    async def _attempt_configuration_merge(self, conflict: ConfigurationConflict, 
                                         new_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Attempt to merge conflicted configurations"""
        try:
            config_key = f"{conflict.config_type}/{conflict.config_name}"
            current_config = self.configurations[conflict.config_type][conflict.config_name]
            
            # Simple merge strategy: prefer new values for non-critical fields
            merged_config = copy.deepcopy(current_config)
            
            def merge_dicts(target: Dict, source: Dict):
                for key, value in source.items():
                    if key not in conflict.conflicted_fields:
                        # No conflict, use new value
                        target[key] = value
                    elif isinstance(value, dict) and isinstance(target.get(key), dict):
                        # Recursive merge for nested dictionaries
                        merge_dicts(target[key], value)
                    else:
                        # For conflicted simple values, use merge logic
                        critical_fields = ["task_name", "model_path", "export_format"]
                        if key in critical_fields:
                            # Keep current value for critical fields
                            pass
                        else:
                            # Use new value for non-critical fields
                            target[key] = value
            
            merge_dicts(merged_config, new_config)
            
            # Validate merged configuration
            validation_result = await self._validate_configuration(conflict.config_type, merged_config)
            
            if validation_result["is_valid"]:
                return merged_config
            else:
                logger.warning(f"Merged configuration validation failed: {validation_result['errors']}")
                return None
                
        except Exception as e:
            logger.error(f"Error attempting configuration merge: {e}")
            return None
    
    async def _start_background_tasks(self):
        """Start background monitoring and maintenance tasks"""
        self.sync_task = asyncio.create_task(self._synchronization_loop())
        self.backup_task = asyncio.create_task(self._backup_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.debug("Configuration service background tasks started")
    
    async def _synchronization_loop(self):
        """Periodic synchronization with external services"""
        try:
            while self.is_initialized:
                # Sync with simulation service
                # Sync with deployment service
                # Update cache
                await asyncio.sleep(self.config.sync_interval)
                
        except asyncio.CancelledError:
            logger.debug("Synchronization loop cancelled")
        except Exception as e:
            logger.error(f"Error in synchronization loop: {e}")
    
    async def _backup_loop(self):
        """Periodic backup of configurations"""
        try:
            while self.is_initialized:
                if self.config.auto_backup_enabled:
                    await self._create_system_backup()
                
                # Wait for next backup interval
                await asyncio.sleep(self.config.backup_interval_hours * 3600)
                
        except asyncio.CancelledError:
            logger.debug("Backup loop cancelled")
        except Exception as e:
            logger.error(f"Error in backup loop: {e}")
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old versions and backups"""
        try:
            while self.is_initialized:
                await self._cleanup_old_versions()
                await self._cleanup_old_backups()
                
                # Wait 24 hours for next cleanup
                await asyncio.sleep(24 * 3600)
                
        except asyncio.CancelledError:
            logger.debug("Cleanup loop cancelled")
        except Exception as e:
            logger.error(f"Error in cleanup loop: {e}")
    
    def _clear_config_cache(self, config_key: str):
        """Clear cache entries for a configuration"""
        keys_to_remove = [key for key in self.config_cache.keys() if key.startswith(config_key)]
        for key in keys_to_remove:
            del self.config_cache[key]
    
    async def _send_configuration_response(self, success: bool, message: str, data: Dict[str, Any] = None):
        """Send configuration response"""
        try:
            response = ConfigurationResponse(
                success=success,
                config_type="system",
                config_data=data or {},
                error_message=message if not success else "",
                timestamp=time.time()
            )
            
            response_message = MessageFactory.create_status(response.to_dict(), "configuration_service")
            await self.topic_manager.publish_message("legged_gym/config/status", response_message)
            
        except Exception as e:
            logger.error(f"Failed to send configuration response: {e}")
    
    async def _publish_configuration_update(self, config_type: str, config_name: str, 
                                          config_data: Dict[str, Any], version_id: Optional[str]):
        """Publish configuration update notification"""
        try:
            update_data = {
                "config_type": config_type,
                "config_name": config_name,
                "config_data": config_data,
                "version_id": version_id,
                "timestamp": time.time()
            }
            
            message = MessageFactory.create_data(update_data, "configuration_service")
            await self.topic_manager.publish_message(topics.CONFIG_UPDATE, message)
            
        except Exception as e:
            logger.error(f"Failed to publish configuration update: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current configuration service status"""
        return {
            "state": self.state.value,
            "is_initialized": self.is_initialized,
            "configurations_count": sum(len(configs) for configs in self.configurations.values()),
            "versions_count": sum(len(versions) for versions in self.versions.values()),
            "pending_conflicts": len(self.pending_conflicts),
            "resolved_conflicts": len(self.resolved_conflicts),
            "snapshots_count": len(self.snapshots),
            "templates_count": len(self.templates),
            "cache_size": len(self.config_cache)
        }
    
    async def _create_configuration_version(self, config_key: str, config_data: Dict[str, Any],
                                          author: str, comment: str) -> str:
        """Create a new configuration version"""
        try:
            async with self.version_lock:
                config_parts = config_key.split("/")
                config_type = config_parts[0]
                config_name = config_parts[1]
                
                # Get next version number
                existing_versions = self.versions.get(config_key, [])
                version_number = len(existing_versions) + 1
                
                # Generate version ID
                version_id = f"{config_key.replace('/', '_')}_v{version_number}_{int(time.time())}"
                
                # Calculate configuration hash
                config_hash = hashlib.md5(json.dumps(config_data, sort_keys=True).encode()).hexdigest()
                
                # Create version metadata
                version = ConfigurationVersion(
                    version_id=version_id,
                    config_type=config_type,
                    config_name=config_name,
                    version_number=version_number,
                    parent_version=self.active_versions.get(config_key),
                    author=author,
                    comment=comment,
                    config_hash=config_hash,
                    file_size=len(json.dumps(config_data)),
                    status=ConfigurationStatus.ACTIVE,
                    is_active=True
                )
                
                # Deactivate previous version
                if config_key in self.versions:
                    for old_version in self.versions[config_key]:
                        old_version.is_active = False
                
                # Add new version
                if config_key not in self.versions:
                    self.versions[config_key] = []
                
                self.versions[config_key].append(version)
                self.active_versions[config_key] = version_id
                
                # Persist version data and metadata
                await self._persist_configuration_version(config_key, version, config_data)
                
                # Limit version history
                if len(self.versions[config_key]) > self.config.max_versions_per_config:
                    await self._archive_old_versions(config_key)
                
                return version_id
                
        except Exception as e:
            logger.error(f"Error creating configuration version: {e}")
            raise
    
    async def _persist_configuration(self, config_type: str, config_name: str, config_data: Dict[str, Any]):
        """Persist configuration to storage"""
        try:
            config_file = Path(self.config.config_storage_path) / config_type / f"{config_name}.json"
            
            # Ensure directory exists
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Write configuration
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Configuration persisted: {config_file}")
            
        except Exception as e:
            logger.error(f"Error persisting configuration: {e}")
            raise
    
    async def _persist_configuration_version(self, config_key: str, version: ConfigurationVersion, 
                                           config_data: Dict[str, Any]):
        """Persist configuration version"""
        try:
            # Create version directory
            versions_dir = Path(self.config.version_storage_path) / config_key.replace("/", "_")
            versions_dir.mkdir(parents=True, exist_ok=True)
            
            # Save version metadata
            metadata_file = versions_dir / f"version_{version.version_number}.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(version.to_dict(), f, indent=2, ensure_ascii=False)
            
            # Save version data
            data_file = versions_dir / f"data_{version.version_number}.json"
            with open(data_file, 'w', encoding='utf-8') as f:
                if self.config.enable_compression:
                    import gzip
                    compressed_data = gzip.compress(json.dumps(config_data).encode('utf-8'))
                    with open(f"{data_file}.gz", 'wb') as gz_file:
                        gz_file.write(compressed_data)
                else:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Version persisted: {metadata_file}")
            
        except Exception as e:
            logger.error(f"Error persisting configuration version: {e}")
            raise
    
    async def _load_configuration_version(self, config_key: str, version_id: str) -> Dict[str, Any]:
        """Load specific configuration version"""
        try:
            versions = self.versions.get(config_key, [])
            version = next((v for v in versions if v.version_id == version_id), None)
            
            if not version:
                raise ValueError(f"Version {version_id} not found")
            
            # Load version data
            versions_dir = Path(self.config.version_storage_path) / config_key.replace("/", "_")
            data_file = versions_dir / f"data_{version.version_number}.json"
            compressed_file = Path(f"{data_file}.gz")
            
            if compressed_file.exists():
                import gzip
                with gzip.open(compressed_file, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            elif data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                raise FileNotFoundError(f"Version data file not found: {data_file}")
                
        except Exception as e:
            logger.error(f"Error loading configuration version: {e}")
            raise
    
    async def _archive_old_versions(self, config_key: str):
        """Archive old versions that exceed the limit"""
        try:
            versions = self.versions[config_key]
            
            # Sort by creation time, keep most recent
            versions.sort(key=lambda v: v.created_at, reverse=True)
            
            versions_to_archive = versions[self.config.max_versions_per_config:]
            
            for version in versions_to_archive:
                version.status = ConfigurationStatus.ARCHIVED
                await self._move_version_to_archive(config_key, version)
            
            # Update versions list
            self.versions[config_key] = versions[:self.config.max_versions_per_config]
            
        except Exception as e:
            logger.error(f"Error archiving old versions: {e}")
    
    async def _move_version_to_archive(self, config_key: str, version: ConfigurationVersion):
        """Move version to archive storage"""
        try:
            archive_dir = Path(self.config.backup_path) / "archived_versions" / config_key.replace("/", "_")
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            versions_dir = Path(self.config.version_storage_path) / config_key.replace("/", "_")
            
            # Move version files
            source_metadata = versions_dir / f"version_{version.version_number}.json"
            source_data = versions_dir / f"data_{version.version_number}.json"
            source_compressed = Path(f"{source_data}.gz")
            
            if source_metadata.exists():
                shutil.move(str(source_metadata), str(archive_dir / source_metadata.name))
            
            if source_compressed.exists():
                shutil.move(str(source_compressed), str(archive_dir / source_compressed.name))
            elif source_data.exists():
                shutil.move(str(source_data), str(archive_dir / source_data.name))
            
        except Exception as e:
            logger.error(f"Error moving version to archive: {e}")
    
    async def _create_system_backup(self):
        """Create full system backup"""
        try:
            async with self.backup_lock:
                backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_dir = Path(self.config.backup_path) / f"system_backup_{backup_timestamp}"
                backup_dir.mkdir(parents=True, exist_ok=True)
                
                # Backup configurations
                config_backup_dir = backup_dir / "configurations"
                if Path(self.config.config_storage_path).exists():
                    shutil.copytree(self.config.config_storage_path, config_backup_dir)
                
                # Backup versions
                versions_backup_dir = backup_dir / "versions"
                if Path(self.config.version_storage_path).exists():
                    shutil.copytree(self.config.version_storage_path, versions_backup_dir)
                
                # Backup metadata
                metadata = {
                    "backup_timestamp": backup_timestamp,
                    "service_state": self.state.value,
                    "configurations_count": sum(len(configs) for configs in self.configurations.values()),
                    "versions_count": sum(len(versions) for versions in self.versions.values()),
                    "active_versions": self.active_versions.copy(),
                    "snapshots": {k: v.to_dict() for k, v in self.snapshots.items()},
                    "pending_conflicts": {k: v.to_dict() for k, v in self.pending_conflicts.items()}
                }
                
                metadata_file = backup_dir / "backup_metadata.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
                
                logger.info(f"System backup created: {backup_dir}")
                
        except Exception as e:
            logger.error(f"Error creating system backup: {e}")
    
    async def _cleanup_old_versions(self):
        """Clean up old versions beyond retention period"""
        try:
            cutoff_time = time.time() - (self.config.auto_cleanup_days * 24 * 3600)
            
            for config_key, versions in self.versions.items():
                versions_to_remove = []
                
                for version in versions:
                    if (version.created_at < cutoff_time and 
                        version.status == ConfigurationStatus.ARCHIVED and
                        not version.is_active):
                        versions_to_remove.append(version)
                
                for version in versions_to_remove:
                    await self._delete_version(config_key, version)
                    versions.remove(version)
            
        except Exception as e:
            logger.error(f"Error cleaning up old versions: {e}")
    
    async def _cleanup_old_backups(self):
        """Clean up old backups"""
        try:
            backup_dir = Path(self.config.backup_path)
            if not backup_dir.exists():
                return
            
            cutoff_time = time.time() - (self.config.auto_cleanup_days * 24 * 3600)
            
            for backup_folder in backup_dir.glob("system_backup_*"):
                if backup_folder.is_dir():
                    folder_time = backup_folder.stat().st_ctime
                    if folder_time < cutoff_time:
                        shutil.rmtree(backup_folder, ignore_errors=True)
                        logger.debug(f"Removed old backup: {backup_folder}")
            
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
    
    async def _delete_version(self, config_key: str, version: ConfigurationVersion):
        """Delete a version and its data files"""
        try:
            versions_dir = Path(self.config.version_storage_path) / config_key.replace("/", "_")
            
            files_to_delete = [
                versions_dir / f"version_{version.version_number}.json",
                versions_dir / f"data_{version.version_number}.json",
                Path(f"{versions_dir / f'data_{version.version_number}.json'}.gz")
            ]
            
            for file_path in files_to_delete:
                if file_path.exists():
                    file_path.unlink()
            
        except Exception as e:
            logger.error(f"Error deleting version: {e}")
    
    async def _request_conflict_resolution(self, conflict: ConfigurationConflict):
        """Request user resolution for configuration conflict"""
        try:
            conflict_data = {
                "conflict": conflict.to_dict(),
                "timestamp": time.time()
            }
            
            message = MessageFactory.create_data(conflict_data, "configuration_service")
            await self.topic_manager.publish_message("legged_gym/config/conflict", message)
            
        except Exception as e:
            logger.error(f"Error requesting conflict resolution: {e}")
    
    async def _create_version_branch(self, conflict: ConfigurationConflict, new_config: Dict[str, Any]):
        """Create a new version branch for conflicted configuration"""
        try:
            config_key = f"{conflict.config_type}/{conflict.config_name}"
            branch_name = f"conflict_branch_{conflict.conflict_id[:8]}"
            
            # Create version with branch name
            version_id = await self._create_configuration_version(
                config_key, new_config, "system", f"Conflict branch: {conflict.conflict_id}"
            )
            
            # Update version with branch name
            versions = self.versions.get(config_key, [])
            for version in versions:
                if version.version_id == version_id:
                    version.branch_name = branch_name
                    break
            
            logger.info(f"Created version branch '{branch_name}' for conflict {conflict.conflict_id}")
            
        except Exception as e:
            logger.error(f"Error creating version branch: {e}")
    
    # Additional handler methods for configuration requests
    async def _handle_validate_configuration(self, request: ConfigurationRequest):
        """Handle configuration validation request"""
        try:
            config_type = request.config_data.get("config_type")
            config_data = request.config_data.get("config_data")
            
            if not config_type or not config_data:
                raise ValueError("config_type and config_data are required")
            
            validation_result = await self._validate_configuration(config_type, config_data)
            
            response_data = {
                "config_type": config_type,
                "validation_result": validation_result
            }
            
            await self._send_configuration_response(True, "Configuration validated", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_list_configurations(self, request: ConfigurationRequest):
        """Handle list configurations request"""
        try:
            config_type = request.config_data.get("config_type")
            
            if config_type:
                configs = self.configurations.get(config_type, {})
                response_data = {"config_type": config_type, "configurations": configs}
            else:
                response_data = {"configurations": self.configurations}
            
            await self._send_configuration_response(True, "Configurations listed", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_create_version(self, request: ConfigurationRequest):
        """Handle create version request"""
        try:
            config_type = request.config_data.get("config_type")
            config_name = request.config_data.get("config_name", "default")
            author = request.config_data.get("author", "system")
            comment = request.config_data.get("comment", "")
            
            config_key = f"{config_type}/{config_name}"
            
            if config_type not in self.configurations or config_name not in self.configurations[config_type]:
                raise ValueError(f"Configuration {config_key} not found")
            
            config_data = self.configurations[config_type][config_name]
            version_id = await self._create_configuration_version(config_key, config_data, author, comment)
            
            response_data = {"version_id": version_id, "config_type": config_type, "config_name": config_name}
            
            await self._send_configuration_response(True, "Version created", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_get_versions(self, request: ConfigurationRequest):
        """Handle get versions request"""
        try:
            config_type = request.config_data.get("config_type")
            config_name = request.config_data.get("config_name", "default")
            
            config_key = f"{config_type}/{config_name}"
            versions = self.versions.get(config_key, [])
            
            response_data = {
                "config_type": config_type,
                "config_name": config_name,
                "versions": [v.to_dict() for v in versions],
                "active_version": self.active_versions.get(config_key)
            }
            
            await self._send_configuration_response(True, "Versions retrieved", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_activate_version(self, request: ConfigurationRequest):
        """Handle activate version request"""
        try:
            config_type = request.config_data.get("config_type")
            config_name = request.config_data.get("config_name", "default")
            version_id = request.config_data.get("version_id")
            
            if not version_id:
                raise ValueError("version_id is required")
            
            config_key = f"{config_type}/{config_name}"
            
            # Load version data
            version_data = await self._load_configuration_version(config_key, version_id)
            
            # Update active configuration
            self.configurations[config_type][config_name] = version_data
            self.active_versions[config_key] = version_id
            
            # Update version status
            versions = self.versions.get(config_key, [])
            for version in versions:
                version.is_active = (version.version_id == version_id)
            
            # Persist changes
            await self._persist_configuration(config_type, config_name, version_data)
            
            response_data = {"version_id": version_id, "config_type": config_type, "config_name": config_name}
            
            await self._send_configuration_response(True, "Version activated", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_create_snapshot(self, request: ConfigurationRequest):
        """Handle create snapshot request"""
        try:
            snapshot_name = request.config_data.get("snapshot_name")
            description = request.config_data.get("description", "")
            
            if not snapshot_name:
                raise ValueError("snapshot_name is required")
            
            # Create snapshot
            snapshot_id = f"snapshot_{int(time.time())}_{hashlib.md5(snapshot_name.encode()).hexdigest()[:8]}"
            
            snapshot = ConfigurationSnapshot(
                snapshot_id=snapshot_id,
                snapshot_name=snapshot_name,
                description=description,
                training_config_version=self.active_versions.get("training/default"),
                simulation_config_version=self.active_versions.get("simulation/default"),
                deployment_config_version=self.active_versions.get("deployment/default")
            )
            
            self.snapshots[snapshot_id] = snapshot
            
            response_data = {"snapshot": snapshot.to_dict()}
            
            await self._send_configuration_response(True, "Snapshot created", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_resolve_conflict(self, request: ConfigurationRequest):
        """Handle conflict resolution request"""
        try:
            conflict_id = request.config_data.get("conflict_id")
            resolution_strategy = request.config_data.get("resolution_strategy")
            
            if not conflict_id or conflict_id not in self.pending_conflicts:
                raise ValueError("Invalid conflict_id")
            
            conflict = self.pending_conflicts[conflict_id]
            
            # Apply resolution strategy
            if resolution_strategy == "accept_current":
                conflict.resolution_status = "resolved"
                conflict.resolved_at = time.time()
            elif resolution_strategy == "accept_incoming":
                # Apply the incoming configuration
                # This would need the original incoming config data
                pass
            elif resolution_strategy == "manual_merge":
                # Apply manually merged configuration
                merged_config = request.config_data.get("merged_config")
                if merged_config:
                    await self.set_configuration(
                        conflict.config_type, conflict.config_name, merged_config,
                        author="user", comment=f"Manual conflict resolution: {conflict_id}"
                    )
            
            # Move to resolved conflicts
            self.resolved_conflicts.append(conflict)
            del self.pending_conflicts[conflict_id]
            
            response_data = {"conflict_id": conflict_id, "resolution_strategy": resolution_strategy}
            
            await self._send_configuration_response(True, "Conflict resolved", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    async def _handle_apply_template(self, request: ConfigurationRequest):
        """Handle apply template request"""
        try:
            template_name = request.config_data.get("template_name")
            config_name = request.config_data.get("config_name", "default")
            variables = request.config_data.get("variables", {})
            
            if not template_name or template_name not in self.templates:
                raise ValueError(f"Template '{template_name}' not found")
            
            template = self.templates[template_name]
            
            # Apply template variables
            config_data = self._apply_template_variables(template["config"], variables)
            
            # Determine config type from template or request
            config_type = request.config_data.get("config_type") or template.get("config_type", "training")
            
            # Set configuration
            version_id = await self.set_configuration(
                config_type, config_name, config_data,
                author="template", comment=f"Applied template: {template_name}"
            )
            
            response_data = {
                "template_name": template_name,
                "config_type": config_type,
                "config_name": config_name,
                "version_id": version_id
            }
            
            await self._send_configuration_response(True, "Template applied", response_data)
            
        except Exception as e:
            await self._send_configuration_response(False, str(e))
    
    def _apply_template_variables(self, template_config: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
        """Apply template variables to configuration"""
        import re
        
        def replace_variables(obj):
            if isinstance(obj, dict):
                return {k: replace_variables(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_variables(item) for item in obj]
            elif isinstance(obj, str):
                # Replace ${VARIABLE} patterns
                def replace_var(match):
                    var_name = match.group(1)
                    return variables.get(var_name, match.group(0))
                
                return re.sub(r'\$\{([^}]+)\}', replace_var, obj)
            else:
                return obj
        
        return replace_variables(copy.deepcopy(template_config))
    
    async def shutdown(self):
        """Shutdown the configuration service"""
        logger.info("Shutting down configuration service...")
        
        self.is_initialized = False
        
        # Cancel background tasks
        tasks = [self.sync_task, self.backup_task, self.cleanup_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
        
        # Create final backup
        if self.config.auto_backup_enabled:
            try:
                await self._create_system_backup()
            except Exception as e:
                logger.warning(f"Final backup failed: {e}")
        
        # Wait for tasks to complete
        await asyncio.sleep(0.1)
        
        self.state = ConfigServiceState.IDLE
        logger.info("Configuration service shutdown complete")


# Factory function for creating configuration service
async def create_configuration_service(
    session_manager: EnhancedZenohSessionManager,
    config: Optional[ConfigServiceConfig] = None
) -> ConfigurationService:
    """Create and initialize a configuration service"""
    service = ConfigurationService(session_manager, config)
    
    if await service.initialize():
        return service
    else:
        raise RuntimeError("Failed to initialize configuration service")