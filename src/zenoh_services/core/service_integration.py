"""
Service Integration Helper for Error Handling
Provides utilities to easily integrate error handling and fault detection into services.
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from abc import ABC, abstractmethod

from .error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>onte<PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, 
    get_error_handler, handle_errors
)
from .fault_detection import (
    FaultDetectionSystem, ServiceMonitor, HealthCheck, 
    RecoveryAction as FaultRecoveryAction, FailureType,
    get_fault_detection_system
)


class ServiceIntegrationMixin:
    """
    Mixin class to add error handling and fault detection to services
    """
    
    def __init__(self, service_name: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service_name = service_name
        self._setup_error_integration()
    
    def _setup_error_integration(self):
        """Setup error handling and fault detection integration"""
        # Initialize error handling components
        self.error_handler = get_error_handler()
        self.fault_detection = get_fault_detection_system()
        self.service_monitor = self.fault_detection.register_service(self.service_name)
        
        # Setup callbacks
        self.error_handler.add_error_callback(self._on_error)
        self.error_handler.add_recovery_callback(self._on_recovery)
        
        # Setup health checks and recovery actions
        self._setup_service_health_checks()
        self._setup_service_recovery_actions()
    
    def _setup_service_health_checks(self):
        """Setup health checks - override in subclass"""
        pass
    
    def _setup_service_recovery_actions(self):
        """Setup recovery actions - override in subclass"""
        pass
    
    def _on_error(self, error_info):
        """Handle error notifications - override in subclass"""
        if error_info.context.service_name == self.service_name:
            logging.getLogger(self.service_name).warning(f"Service error: {error_info.message}")
    
    def _on_recovery(self, service_name: str, recovery_action: str, success: bool):
        """Handle recovery notifications - override in subclass"""
        if service_name == self.service_name:
            logger = logging.getLogger(self.service_name)
            if success:
                logger.info(f"Recovery successful: {recovery_action}")
            else:
                logger.error(f"Recovery failed: {recovery_action}")
    
    def create_error_context(self, function_name: str, additional_data: Dict[str, Any] = None) -> ErrorContext:
        """Create error context for this service"""
        return ErrorContext(
            service_name=self.service_name,
            service_state=getattr(self, 'state', 'unknown'),
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name=function_name,
            additional_data=additional_data or {}
        )
    
    async def start_monitoring(self):
        """Start service monitoring"""
        if hasattr(self.service_monitor, 'start_monitoring'):
            await self.service_monitor.start_monitoring()
    
    async def stop_monitoring(self):
        """Stop service monitoring"""
        if hasattr(self.service_monitor, 'stop_monitoring'):
            await self.service_monitor.stop_monitoring()
    
    def add_health_check(self, name: str, check_function: Callable[[], bool], 
                        interval: float = 30.0, timeout: float = 10.0):
        """Add a health check"""
        health_check = HealthCheck(
            name=name,
            check_function=check_function,
            interval=interval,
            timeout=timeout
        )
        self.service_monitor.add_health_check(health_check)
    
    def add_recovery_action(self, failure_type: FailureType, action_name: str, 
                          action_function: Callable[[], bool]):
        """Add a recovery action"""
        recovery_action = FaultRecoveryAction(
            action_name=action_name,
            action_function=action_function
        )
        self.service_monitor.add_recovery_action(failure_type, recovery_action)


class BaseService(ServiceIntegrationMixin):
    """
    Base service class with integrated error handling
    """
    
    def __init__(self, service_name: str, session_manager=None):
        self.session_manager = session_manager
        self.logger = logging.getLogger(service_name)
        self.is_initialized = False
        self.is_running = False
        super().__init__(service_name)
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the service - must be implemented by subclass"""
        pass
    
    @abstractmethod
    async def start(self) -> bool:
        """Start the service - must be implemented by subclass"""
        pass
    
    @abstractmethod
    async def stop(self) -> bool:
        """Stop the service - must be implemented by subclass"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """Clean up service resources - must be implemented by subclass"""
        pass
    
    async def restart(self) -> bool:
        """Restart the service"""
        try:
            self.logger.info(f"Restarting service: {self.service_name}")
            
            # Stop current operation
            if self.is_running:
                await self.stop()
            
            # Clean up
            await self.cleanup()
            
            # Reinitialize and start
            if await self.initialize():
                return await self.start()
            
            return False
            
        except Exception as e:
            context = self.create_error_context("restart")
            await self.error_handler.handle_error(e, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH)
            return False


def service_method_error_handler(
    category: ErrorCategory = ErrorCategory.SERVICE,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
):
    """
    Decorator to add error handling to service methods
    """
    def decorator(func):
        async def async_wrapper(self, *args, **kwargs):
            try:
                return await func(self, *args, **kwargs)
            except Exception as e:
                if hasattr(self, 'create_error_context'):
                    context = self.create_error_context(func.__name__)
                else:
                    context = ErrorContext(
                        service_name=getattr(self, 'service_name', 'unknown_service'),
                        service_state=getattr(self, 'state', 'unknown'),
                        timestamp=time.time(),
                        thread_id=threading.get_ident(),
                        function_name=func.__name__
                    )
                
                if hasattr(self, 'error_handler'):
                    await self.error_handler.handle_error(e, context, category, severity)
                else:
                    error_handler = get_error_handler()
                    await error_handler.handle_error(e, context, category, severity)
                
                raise
        
        def sync_wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                if hasattr(self, 'create_error_context'):
                    context = self.create_error_context(func.__name__)
                else:
                    context = ErrorContext(
                        service_name=getattr(self, 'service_name', 'unknown_service'),
                        service_state=getattr(self, 'state', 'unknown'),
                        timestamp=time.time(),
                        thread_id=threading.get_ident(),
                        function_name=func.__name__
                    )
                
                error_handler = get_error_handler()
                # For sync functions, run async operation in event loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(error_handler.handle_error(e, context, category, severity))
                loop.close()
                
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ServiceHealthChecks:
    """
    Common health checks for services
    """
    
    @staticmethod
    def session_manager_health(session_manager) -> Callable[[], bool]:
        """Health check for session manager"""
        def check():
            try:
                return session_manager is not None and session_manager.is_connected()
            except:
                return False
        return check
    
    @staticmethod
    def memory_usage_health(threshold: float = 0.9) -> Callable[[], bool]:
        """Health check for memory usage"""
        def check():
            try:
                import psutil
                return psutil.virtual_memory().percent / 100.0 < threshold
            except:
                return True  # If can't check, assume OK
        return check
    
    @staticmethod
    def cpu_usage_health(threshold: float = 0.9) -> Callable[[], bool]:
        """Health check for CPU usage"""
        def check():
            try:
                import psutil
                return psutil.cpu_percent() / 100.0 < threshold
            except:
                return True  # If can't check, assume OK
        return check
    
    @staticmethod
    def gpu_memory_health(threshold: float = 0.9) -> Callable[[], bool]:
        """Health check for GPU memory usage"""
        def check():
            try:
                import torch
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated()
                    cached = torch.cuda.memory_cached()
                    total = torch.cuda.get_device_properties(0).total_memory
                    usage = (allocated + cached) / total
                    return usage < threshold
                return True
            except:
                return True
        return check
    
    @staticmethod
    def service_state_health(service, healthy_states: List[str]) -> Callable[[], bool]:
        """Health check for service state"""
        def check():
            try:
                if hasattr(service, 'state'):
                    return service.state in healthy_states or service.state.value in healthy_states
                return True
            except:
                return False
        return check


class ServiceRecoveryActions:
    """
    Common recovery actions for services
    """
    
    @staticmethod
    def restart_service_action(service) -> Callable[[], bool]:
        """Recovery action to restart service"""
        def action():
            try:
                if hasattr(service, 'restart'):
                    asyncio.create_task(service.restart())
                    return True
                return False
            except:
                return False
        return action
    
    @staticmethod
    def cleanup_memory_action() -> Callable[[], bool]:
        """Recovery action to clean up memory"""
        def action():
            try:
                import gc
                gc.collect()
                
                # GPU memory cleanup if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                except:
                    pass
                
                return True
            except:
                return False
        return action
    
    @staticmethod
    def reset_connections_action(session_manager) -> Callable[[], bool]:
        """Recovery action to reset connections"""
        def action():
            try:
                if hasattr(session_manager, 'reconnect'):
                    asyncio.create_task(session_manager.reconnect())
                    return True
                return False
            except:
                return False
        return action


# Convenience function to quickly set up error handling for existing services
def integrate_error_handling(service, service_name: str, 
                           health_checks: List[Dict[str, Any]] = None,
                           recovery_actions: List[Dict[str, Any]] = None):
    """
    Integrate error handling into an existing service
    
    Args:
        service: The service instance
        service_name: Name of the service
        health_checks: List of health check configurations
        recovery_actions: List of recovery action configurations
    """
    # Add error handling components
    service.service_name = service_name
    service.error_handler = get_error_handler()
    service.fault_detection = get_fault_detection_system()
    service.service_monitor = service.fault_detection.register_service(service_name)
    
    # Add create_error_context method
    def create_error_context(function_name: str, additional_data: Dict[str, Any] = None) -> ErrorContext:
        return ErrorContext(
            service_name=service_name,
            service_state=getattr(service, 'state', 'unknown'),
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name=function_name,
            additional_data=additional_data or {}
        )
    service.create_error_context = create_error_context
    
    # Add health checks
    if health_checks:
        for hc in health_checks:
            health_check = HealthCheck(**hc)
            service.service_monitor.add_health_check(health_check)
    
    # Add recovery actions
    if recovery_actions:
        for ra in recovery_actions:
            recovery_action = FaultRecoveryAction(
                action_name=ra['action_name'],
                action_function=ra['action_function']
            )
            service.service_monitor.add_recovery_action(ra['failure_type'], recovery_action)
    
    return service