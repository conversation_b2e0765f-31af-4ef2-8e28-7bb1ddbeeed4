import asyncio
import logging
import time
from typing import Dict, Optional, Callable, Any
import zenoh
import msgpack
from dataclasses import dataclass, asdict, field

logger = logging.getLogger(__name__)

@dataclass
class ZenohMessage:
    """Standard message format for Zenoh communications"""
    timestamp: float
    message_id: str
    message_type: str
    payload: dict
    metadata: dict = field(default_factory=dict)
    
    def to_msgpack(self) -> bytes:
        return msgpack.packb(asdict(self))
    
    @classmethod
    def from_msgpack(cls, data: bytes):
        return cls(**msgpack.unpackb(data, raw=False))

@dataclass
class ZenohConfig:
    """Configuration for Zenoh session"""
    router_endpoint: str = "tcp/127.0.0.1:7447"
    session_timeout: int = 30
    qos_reliability: str = "reliable"
    qos_durability: str = "volatile"

class ZenohSessionManager:
    """
    Manages Zenoh session, publishers, and subscribers for the legged robot training system
    """
    
    def __init__(self, config: ZenohConfig = None):
        self.config = config or ZenohConfig()
        self.session = None
        self.publishers = {}
        self.subscribers = {}
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize the Zenoh session"""
        try:
            # Configure Zenoh session  
            zenoh_config = zenoh.Config()
            if self.config.router_endpoint:
                # Use the proper Zenoh configuration method
                zenoh_config = zenoh.Config()
            
            # Open the session
            self.session = zenoh.open(zenoh_config)
            self.is_initialized = True
            logger.info(f"Zenoh session initialized with router: {self.config.router_endpoint}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Zenoh session: {e}")
            raise
    
    async def create_publisher(self, topic: str):
        """Create a publisher for the given topic"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        if topic not in self.publishers:
            self.publishers[topic] = self.session.declare_publisher(topic)
            logger.info(f"Created publisher for topic: {topic}")
        
        return self.publishers[topic]
    
    async def create_subscriber(self, topic: str, callback: Callable[[Any], None]):
        """Create a subscriber for the given topic with callback"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        def zenoh_callback(sample):
            try:
                # Deserialize the message
                message = ZenohMessage.from_msgpack(sample.payload)
                callback(message)
            except Exception as e:
                logger.error(f"Error processing message from topic {topic}: {e}")
        
        if topic not in self.subscribers:
            self.subscribers[topic] = self.session.declare_subscriber(topic, zenoh_callback)
            logger.info(f"Created subscriber for topic: {topic}")
        
        return self.subscribers[topic]
    
    async def publish(self, topic: str, data: dict, message_type: str = "data"):
        """Publish data to the specified topic"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        # Create publisher if it doesn't exist
        if topic not in self.publishers:
            await self.create_publisher(topic)
        
        # Create standardized message
        message = ZenohMessage(
            timestamp=time.time(),
            message_id=f"{topic}_{int(time.time() * 1000)}",
            message_type=message_type,
            payload=data
        )
        
        try:
            # Publish the message
            self.publishers[topic].put(message.to_msgpack())
            logger.debug(f"Published message to topic: {topic}")
            
        except Exception as e:
            logger.error(f"Failed to publish message to topic {topic}: {e}")
            raise
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            # Close publishers
            for topic, publisher in self.publishers.items():
                publisher.undeclare()
                logger.debug(f"Closed publisher for topic: {topic}")
            
            # Close subscribers
            for topic, subscriber in self.subscribers.items():
                subscriber.undeclare()
                logger.debug(f"Closed subscriber for topic: {topic}")
            
            # Close session
            if self.session:
                self.session.close()
                logger.info("Zenoh session closed")
                
            self.is_initialized = False
            self.publishers.clear()
            self.subscribers.clear()
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        if self.is_initialized:
            try:
                asyncio.create_task(self.cleanup())
            except:
                pass