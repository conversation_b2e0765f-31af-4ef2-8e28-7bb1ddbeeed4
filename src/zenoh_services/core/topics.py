# Topic definitions for the legged robot training system

# System status topics
SYSTEM_STATUS = "legged_gym/system/status"
SYSTEM_HEALTH = "legged_gym/system/health"
SYSTEM_ERROR = "legged_gym/system/error"

# Training related topics
TRAINING_COMMAND = "legged_gym/training/command"
TRAINING_STATUS = "legged_gym/training/status"
TRAINING_METRICS = "legged_gym/training/metrics"
TRAINING_PROGRESS = "legged_gym/training/progress"
TRAINING_CHECKPOINT = "legged_gym/training/checkpoint"

# Legacy training topics (for compatibility)
TRAIN_COMMAND = "legged_gym/train/command"
TRAIN_STATUS = "legged_gym/train/status"
TRAIN_METRICS = "legged_gym/train/metrics"
TRAIN_PROGRESS = "legged_gym/train/progress"
TRAIN_CHECKPOINT = "legged_gym/train/checkpoint"

# Testing/Play related topics
PLAY_COMMAND = "legged_gym/play/command"
PLAY_STATUS = "legged_gym/play/status"
ROBOT_STATE = "legged_gym/robot/state"
ROBOT_CONTROL = "legged_gym/robot/control"
ROBOT_COMMAND = "legged_gym/robot/command"
ROBOT_CONTROL_COMMAND = "legged_gym/robot/control/command"

# Simulation related topics
SIMULATION_CONFIG = "legged_gym/simulation/config"
SIMULATION_STATUS = "legged_gym/simulation/status"
SIMULATION_METRICS = "legged_gym/simulation/metrics"
TERRAIN_CONFIG = "legged_gym/simulation/terrain/config"
PHYSICS_CONFIG = "legged_gym/simulation/physics/config"
ROBOT_CONFIG = "legged_gym/simulation/robot/config"

# Legacy simulation topics (for compatibility)
SIM_CONFIG = "legged_gym/simulation/config"
SIM_STATUS = "legged_gym/simulation/status"
SIM_METRICS = "legged_gym/simulation/metrics"

# Deployment related topics
DEPLOYMENT_COMMAND = "legged_gym/deployment/command"
DEPLOYMENT_STATUS = "legged_gym/deployment/status"
MODEL_INFO = "legged_gym/deployment/model/info"

# Legacy deployment topics (for compatibility)
DEPLOY_COMMAND = "legged_gym/deploy/command"
DEPLOY_STATUS = "legged_gym/deploy/status"

# Configuration management topics
CONFIG_UPDATE = "legged_gym/config/update"
CONFIG_REQUEST = "legged_gym/config/request"

# Environment management topics
ENV_STATUS = "legged_gym/environment/status"
ENV_COMMAND = "legged_gym/environment/command"