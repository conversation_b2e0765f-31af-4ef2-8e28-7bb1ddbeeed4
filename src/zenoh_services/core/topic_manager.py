"""
Advanced topic management and routing configuration for Zenoh services.
"""

import asyncio
import logging
import re
from typing import Dict, List, Set, Optional, Callable, Any, Pattern
from dataclasses import dataclass, field
from enum import Enum
import time
from collections import defaultdict, deque

from .enhanced_session_manager import EnhancedZenohSessionManager, TopicConfig, QoSLevel
from .message_format import StandardZenohMessage, MessageType, Priority, MessageFactory
from .data_models import SerializationMixin
from . import topics

logger = logging.getLogger(__name__)

class RoutingStrategy(Enum):
    """Message routing strategies"""
    DIRECT = "direct"              # Direct topic-to-topic routing
    BROADCAST = "broadcast"        # Send to all matching subscribers
    ROUND_ROBIN = "round_robin"    # Load balance across subscribers
    PRIORITY_BASED = "priority"    # Route based on message priority
    CONTENT_BASED = "content"      # Route based on message content

class TopicType(Enum):
    """Topic type classification"""
    COMMAND = "command"            # Command topics (commands, requests)
    STATUS = "status"              # Status topics (status updates)
    DATA = "data"                  # Data topics (sensor data, metrics)
    ERROR = "error"                # Error topics
    HEARTBEAT = "heartbeat"        # Heartbeat topics
    CONFIG = "config"              # Configuration topics

@dataclass
class TopicPattern:
    """Pattern matching for topic routing"""
    pattern: str                                    # Topic pattern (supports wildcards)
    topic_type: TopicType                          # Type of topic
    routing_strategy: RoutingStrategy = RoutingStrategy.DIRECT
    priority: Priority = Priority.NORMAL
    qos_config: Optional[TopicConfig] = None
    description: str = ""
    
    def __post_init__(self):
        """Compile regex pattern for matching"""
        # Convert wildcard pattern to regex
        regex_pattern = self.pattern.replace("*", "([^/]+)").replace("+", "(.+)")
        self.compiled_pattern: Pattern = re.compile(f"^{regex_pattern}$")
    
    def matches(self, topic: str) -> bool:
        """Check if topic matches this pattern"""
        return bool(self.compiled_pattern.match(topic))
    
    def extract_parameters(self, topic: str) -> Dict[str, str]:
        """Extract parameters from topic using pattern"""
        match = self.compiled_pattern.match(topic)
        if match:
            return {"param_" + str(i): value for i, value in enumerate(match.groups(), 1)}
        return {}

@dataclass 
class RoutingRule:
    """Routing rule for message forwarding"""
    source_pattern: str
    destination_patterns: List[str]
    condition_func: Optional[Callable[[StandardZenohMessage], bool]] = None
    transformation_func: Optional[Callable[[StandardZenohMessage], StandardZenohMessage]] = None
    priority: int = 0
    enabled: bool = True

@dataclass
class TopicMetrics:
    """Metrics for individual topics"""
    topic_name: str
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    last_activity: float = 0.0
    error_count: int = 0
    subscriber_count: int = 0
    avg_message_size: float = 0.0
    message_rate: float = 0.0  # messages per second
    
    def update_send_metrics(self, message_size: int):
        """Update metrics for sent message"""
        self.messages_sent += 1
        self.bytes_sent += message_size
        self.last_activity = time.time()
        self._update_avg_message_size()
    
    def update_receive_metrics(self, message_size: int):
        """Update metrics for received message"""
        self.messages_received += 1
        self.bytes_received += message_size
        self.last_activity = time.time()
        self._update_avg_message_size()
    
    def _update_avg_message_size(self):
        """Update average message size"""
        total_messages = self.messages_sent + self.messages_received
        total_bytes = self.bytes_sent + self.bytes_received
        if total_messages > 0:
            self.avg_message_size = total_bytes / total_messages

class TopicManager:
    """
    Advanced topic management system with routing, patterns, and metrics.
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager):
        self.session_manager = session_manager
        
        # Topic management
        self.registered_topics: Dict[str, TopicConfig] = {}
        self.topic_patterns: List[TopicPattern] = []
        self.routing_rules: List[RoutingRule] = []
        self.topic_hierarchy: Dict[str, Set[str]] = defaultdict(set)
        
        # Metrics and monitoring
        self.topic_metrics: Dict[str, TopicMetrics] = {}
        self.message_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Callbacks and subscribers
        self.topic_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        self.pattern_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Initialize standard topic patterns
        self._initialize_standard_patterns()
    
    def _initialize_standard_patterns(self):
        """Initialize standard topic patterns for the legged robot system"""
        standard_patterns = [
            # System topics
            TopicPattern(
                pattern="legged_gym/system/*",
                topic_type=TopicType.STATUS,
                routing_strategy=RoutingStrategy.BROADCAST,
                priority=Priority.HIGH,
                description="System status and health topics"
            ),
            
            # Training topics
            TopicPattern(
                pattern="legged_gym/train/command",
                topic_type=TopicType.COMMAND,
                routing_strategy=RoutingStrategy.DIRECT,
                priority=Priority.HIGH,
                description="Training command topic"
            ),
            TopicPattern(
                pattern="legged_gym/train/*",
                topic_type=TopicType.STATUS,
                routing_strategy=RoutingStrategy.BROADCAST,
                priority=Priority.NORMAL,
                description="Training status and metrics"
            ),
            
            # Robot control and state
            TopicPattern(
                pattern="legged_gym/robot/control",
                topic_type=TopicType.COMMAND,
                routing_strategy=RoutingStrategy.DIRECT,
                priority=Priority.CRITICAL,
                description="Robot control commands"
            ),
            TopicPattern(
                pattern="legged_gym/robot/state",
                topic_type=TopicType.DATA,
                routing_strategy=RoutingStrategy.BROADCAST,
                priority=Priority.NORMAL,
                description="Robot state data"
            ),
            
            # Simulation topics
            TopicPattern(
                pattern="legged_gym/simulation/*",
                topic_type=TopicType.STATUS,
                routing_strategy=RoutingStrategy.BROADCAST,
                priority=Priority.NORMAL,
                description="Simulation configuration and status"
            ),
            
            # Deployment topics
            TopicPattern(
                pattern="legged_gym/deploy/*",
                topic_type=TopicType.COMMAND,
                routing_strategy=RoutingStrategy.DIRECT,
                priority=Priority.NORMAL,
                description="Model deployment topics"
            ),
            
            # Configuration topics
            TopicPattern(
                pattern="legged_gym/config/*",
                topic_type=TopicType.CONFIG,
                routing_strategy=RoutingStrategy.DIRECT,
                priority=Priority.NORMAL,
                description="Configuration management"
            )
        ]
        
        self.topic_patterns.extend(standard_patterns)
        logger.info(f"Initialized {len(standard_patterns)} standard topic patterns")
    
    async def register_topic(self, topic_name: str, config: TopicConfig = None) -> bool:
        """Register a topic with configuration"""
        try:
            # Find matching pattern
            pattern_match = self._find_matching_pattern(topic_name)
            
            # Create config if not provided
            if config is None:
                config = TopicConfig(
                    topic_name=topic_name,
                    qos_reliability=QoSLevel.RELIABLE if pattern_match and pattern_match.priority in [Priority.HIGH, Priority.CRITICAL] else QoSLevel.BEST_EFFORT,
                    qos_durability=QoSLevel.PERSISTENT if pattern_match and pattern_match.topic_type in [TopicType.STATUS, TopicType.CONFIG] else QoSLevel.PERSISTENT,
                    message_retention_count=100 if pattern_match and pattern_match.topic_type == TopicType.DATA else 10
                )
            
            # Apply pattern-based config overrides
            if pattern_match and pattern_match.qos_config:
                config = pattern_match.qos_config
            
            # Register with session manager
            await self.session_manager.register_topic(topic_name, config)
            
            # Store locally
            self.registered_topics[topic_name] = config
            
            # Initialize metrics
            self.topic_metrics[topic_name] = TopicMetrics(topic_name)
            
            # Build topic hierarchy
            self._update_topic_hierarchy(topic_name)
            
            logger.info(f"Registered topic: {topic_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register topic {topic_name}: {e}")
            return False
    
    def _find_matching_pattern(self, topic_name: str) -> Optional[TopicPattern]:
        """Find the first matching pattern for a topic"""
        for pattern in self.topic_patterns:
            if pattern.matches(topic_name):
                return pattern
        return None
    
    def _update_topic_hierarchy(self, topic_name: str):
        """Update topic hierarchy for efficient routing"""
        parts = topic_name.split('/')
        for i in range(1, len(parts)):
            parent = '/'.join(parts[:i])
            child = '/'.join(parts[:i+1])
            self.topic_hierarchy[parent].add(child)
    
    async def create_publisher(self, topic: str, auto_register: bool = True) -> bool:
        """Create publisher with automatic topic registration"""
        try:
            if auto_register and topic not in self.registered_topics:
                await self.register_topic(topic)
            
            await self.session_manager.create_publisher(topic)
            
            # Update metrics
            if topic in self.topic_metrics:
                self.topic_metrics[topic].subscriber_count += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create publisher for {topic}: {e}")
            if topic in self.topic_metrics:
                self.topic_metrics[topic].error_count += 1
            return False
    
    async def create_subscriber(self, topic: str, callback: Callable, auto_register: bool = True) -> bool:
        """Create subscriber with enhanced callback management"""
        try:
            if auto_register and topic not in self.registered_topics:
                await self.register_topic(topic)
            
            # Wrap callback with metrics and routing logic
            enhanced_callback = self._create_enhanced_callback(topic, callback)
            
            await self.session_manager.create_subscriber(topic, enhanced_callback)
            
            # Store callback reference
            self.topic_callbacks[topic].append(callback)
            
            # Update metrics
            if topic in self.topic_metrics:
                self.topic_metrics[topic].subscriber_count += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create subscriber for {topic}: {e}")
            if topic in self.topic_metrics:
                self.topic_metrics[topic].error_count += 1
            return False
    
    def _create_enhanced_callback(self, topic: str, original_callback: Callable) -> Callable:
        """Create enhanced callback with metrics and routing"""
        async def enhanced_callback(message):
            try:
                # Update metrics
                if topic in self.topic_metrics:
                    message_size = len(message.to_msgpack()) if hasattr(message, 'to_msgpack') else 0
                    self.topic_metrics[topic].update_receive_metrics(message_size)
                
                # Add to message history
                self.message_history[topic].append({
                    'timestamp': time.time(),
                    'message': message,
                    'size': message_size
                })
                
                # Apply routing rules
                await self._apply_routing_rules(topic, message)
                
                # Call original callback
                if asyncio.iscoroutinefunction(original_callback):
                    await original_callback(message)
                else:
                    original_callback(message)
                
            except Exception as e:
                logger.error(f"Error in enhanced callback for topic {topic}: {e}")
                if topic in self.topic_metrics:
                    self.topic_metrics[topic].error_count += 1
        
        return enhanced_callback
    
    async def _apply_routing_rules(self, source_topic: str, message: StandardZenohMessage):
        """Apply routing rules to forward messages"""
        try:
            for rule in self.routing_rules:
                if not rule.enabled:
                    continue
                
                # Check if source matches rule pattern
                source_pattern = re.compile(rule.source_pattern.replace("*", "([^/]+)").replace("+", "(.+)"))
                if not source_pattern.match(source_topic):
                    continue
                
                # Check condition if specified
                if rule.condition_func and not rule.condition_func(message):
                    continue
                
                # Apply transformation if specified
                routed_message = message
                if rule.transformation_func:
                    routed_message = rule.transformation_func(message)
                
                # Forward to destination topics
                for dest_pattern in rule.destination_patterns:
                    # Expand destination pattern
                    dest_topics = self._expand_destination_pattern(dest_pattern, source_topic)
                    
                    for dest_topic in dest_topics:
                        await self.publish_message(dest_topic, routed_message)
                        
        except Exception as e:
            logger.error(f"Error applying routing rules: {e}")
    
    def _expand_destination_pattern(self, pattern: str, source_topic: str) -> List[str]:
        """Expand destination pattern to actual topic names"""
        # For now, return pattern as-is
        # In a full implementation, this would handle parameter substitution
        return [pattern]
    
    async def publish_message(self, topic: str, message: StandardZenohMessage, auto_create: bool = True):
        """Publish message with enhanced routing and metrics"""
        try:
            # Auto-create publisher if needed
            if auto_create and topic not in self.session_manager.publishers:
                await self.create_publisher(topic)
            
            # Apply routing strategy
            pattern_match = self._find_matching_pattern(topic)
            if pattern_match:
                await self._apply_routing_strategy(topic, message, pattern_match.routing_strategy)
            else:
                # Default direct routing
                await self.session_manager.publish_message(topic, message)
            
            # Update metrics
            if topic in self.topic_metrics:
                message_size = len(message.to_msgpack())
                self.topic_metrics[topic].update_send_metrics(message_size)
            
        except Exception as e:
            logger.error(f"Failed to publish message to {topic}: {e}")
            if topic in self.topic_metrics:
                self.topic_metrics[topic].error_count += 1
            raise
    
    async def _apply_routing_strategy(self, topic: str, message: StandardZenohMessage, strategy: RoutingStrategy):
        """Apply specific routing strategy"""
        if strategy == RoutingStrategy.DIRECT:
            await self.session_manager.publish_message(topic, message)
        
        elif strategy == RoutingStrategy.BROADCAST:
            # Find all related topics for broadcasting
            related_topics = self._find_related_topics(topic)
            for related_topic in related_topics:
                try:
                    await self.session_manager.publish_message(related_topic, message)
                except Exception as e:
                    logger.warning(f"Failed to broadcast to {related_topic}: {e}")
        
        elif strategy == RoutingStrategy.PRIORITY_BASED:
            # Modify message based on priority
            if message.header.priority == Priority.CRITICAL:
                # Use reliable QoS for critical messages
                pass
            await self.session_manager.publish_message(topic, message)
        
        else:
            # Default to direct routing
            await self.session_manager.publish_message(topic, message)
    
    def _find_related_topics(self, topic: str) -> List[str]:
        """Find topics related to the given topic for broadcasting"""
        # Simple implementation - find topics with same prefix
        prefix = '/'.join(topic.split('/')[:-1])
        related = []
        
        for registered_topic in self.registered_topics:
            if registered_topic.startswith(prefix) and registered_topic != topic:
                related.append(registered_topic)
        
        return related
    
    async def add_routing_rule(self, rule: RoutingRule):
        """Add a new routing rule"""
        self.routing_rules.append(rule)
        # Sort by priority (higher priority first)
        self.routing_rules.sort(key=lambda r: r.priority, reverse=True)
        logger.info(f"Added routing rule: {rule.source_pattern} -> {rule.destination_patterns}")
    
    async def remove_routing_rule(self, source_pattern: str):
        """Remove routing rule by source pattern"""
        self.routing_rules = [rule for rule in self.routing_rules if rule.source_pattern != source_pattern]
        logger.info(f"Removed routing rule for pattern: {source_pattern}")
    
    def add_topic_pattern(self, pattern: TopicPattern):
        """Add a new topic pattern"""
        self.topic_patterns.append(pattern)
        logger.info(f"Added topic pattern: {pattern.pattern}")
    
    async def get_topic_metrics(self, topic: str = None) -> Dict[str, Any]:
        """Get metrics for specific topic or all topics"""
        if topic:
            if topic in self.topic_metrics:
                return {
                    "topic": topic,
                    "metrics": self.topic_metrics[topic].__dict__,
                    "config": self.registered_topics.get(topic).__dict__ if topic in self.registered_topics else None,
                    "message_history_count": len(self.message_history[topic]),
                    "callback_count": len(self.topic_callbacks[topic])
                }
            else:
                return {"error": f"Topic {topic} not found"}
        else:
            # Return all topic metrics
            return {
                "topics": {
                    t: {
                        "metrics": metrics.__dict__,
                        "config": self.registered_topics.get(t).__dict__ if t in self.registered_topics else None,
                        "message_history_count": len(self.message_history[t]),
                        "callback_count": len(self.topic_callbacks[t])
                    }
                    for t, metrics in self.topic_metrics.items()
                },
                "total_topics": len(self.topic_metrics),
                "total_patterns": len(self.topic_patterns),
                "total_routing_rules": len(self.routing_rules)
            }
    
    async def get_topic_hierarchy(self) -> Dict[str, Any]:
        """Get topic hierarchy for visualization"""
        return {
            "hierarchy": dict(self.topic_hierarchy),
            "registered_topics": list(self.registered_topics.keys()),
            "patterns": [p.pattern for p in self.topic_patterns]
        }
    
    async def cleanup_inactive_topics(self, inactive_threshold: float = 300.0):
        """Clean up topics that haven't been active for a specified time"""
        current_time = time.time()
        inactive_topics = []
        
        for topic, metrics in self.topic_metrics.items():
            if current_time - metrics.last_activity > inactive_threshold:
                inactive_topics.append(topic)
        
        for topic in inactive_topics:
            try:
                # Remove from session manager
                if topic in self.session_manager.publishers:
                    self.session_manager.publishers[topic].undeclare()
                    del self.session_manager.publishers[topic]
                
                if topic in self.session_manager.subscribers:
                    self.session_manager.subscribers[topic].undeclare()
                    del self.session_manager.subscribers[topic]
                
                # Clean up local data
                del self.topic_metrics[topic]
                if topic in self.topic_callbacks:
                    del self.topic_callbacks[topic]
                if topic in self.message_history:
                    del self.message_history[topic]
                
                logger.info(f"Cleaned up inactive topic: {topic}")
                
            except Exception as e:
                logger.error(f"Error cleaning up topic {topic}: {e}")
        
        return inactive_topics