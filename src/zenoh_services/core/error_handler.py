"""
Unified Error Handling and Recovery System for Zenoh Services
Provides centralized error handling, logging, and recovery mechanisms.
"""

import asyncio
import logging
import traceback
import time
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import threading
import json
from pathlib import Path


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    SERVICE = "service"
    TRAINING = "training"
    SIMULATION = "simulation"
    DEPLOYMENT = "deployment"
    CONFIGURATION = "configuration"
    RESOURCE = "resource"
    ZENOH = "zenoh"
    HARDWARE = "hardware"
    DATA = "data"


class RecoveryAction(Enum):
    """Available recovery actions"""
    RETRY = "retry"
    RESTART_SERVICE = "restart_service"
    RECONNECT = "reconnect"
    RESET_STATE = "reset_state"
    FALLBACK_MODE = "fallback_mode"
    MANUAL_INTERVENTION = "manual_intervention"
    IGNORE = "ignore"


@dataclass
class ErrorContext:
    """Context information for an error"""
    service_name: str
    service_state: str
    timestamp: float
    thread_id: int
    function_name: str
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorInfo:
    """Comprehensive error information"""
    error_id: str
    timestamp: float
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    exception_type: str
    traceback: str
    context: ErrorContext
    retry_count: int = 0
    resolved: bool = False
    resolution_timestamp: Optional[float] = None
    resolution_method: Optional[str] = None


@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration"""
    error_pattern: str  # Regex pattern to match errors
    category: ErrorCategory
    severity: ErrorSeverity
    action: RecoveryAction
    max_retry_attempts: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0
    custom_handler: Optional[Callable] = None
    requires_manual_approval: bool = False


class HealthStatus(Enum):
    """Service health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"


@dataclass
class ServiceHealth:
    """Service health information"""
    service_name: str
    status: HealthStatus
    last_check: float
    error_count: int = 0
    recovery_attempts: int = 0
    uptime: float = 0.0
    metrics: Dict[str, Any] = field(default_factory=dict)


class ErrorHandler:
    """Centralized error handler with recovery capabilities"""
    
    def __init__(self, log_file: Optional[str] = None, enable_recovery: bool = True):
        self.logger = self._setup_logger(log_file)
        self.enable_recovery = enable_recovery
        
        # Error tracking
        self.errors: Dict[str, ErrorInfo] = {}
        self.error_history: List[ErrorInfo] = []
        self.recovery_strategies: List[RecoveryStrategy] = []
        
        # Service health tracking
        self.service_health: Dict[str, ServiceHealth] = {}
        
        # Recovery state
        self.recovery_in_progress: Dict[str, bool] = {}
        self.recovery_tasks: Dict[str, asyncio.Task] = {}
        
        # Notification system
        self.error_callbacks: List[Callable[[ErrorInfo], None]] = []
        self.recovery_callbacks: List[Callable[[str, RecoveryAction, bool], None]] = []
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Default recovery strategies
        self._register_default_strategies()
    
    def _setup_logger(self, log_file: Optional[str] = None) -> logging.Logger:
        """Setup centralized logger"""
        logger = logging.getLogger("zenoh_error_handler")
        logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _register_default_strategies(self):
        """Register default recovery strategies"""
        
        # Network connection errors
        self.register_strategy(RecoveryStrategy(
            error_pattern=r".*connection.*failed.*|.*timeout.*|.*network.*unreachable.*",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RECONNECT,
            max_retry_attempts=5,
            retry_delay=2.0,
            timeout=30.0
        ))
        
        # Zenoh session errors
        self.register_strategy(RecoveryStrategy(
            error_pattern=r".*zenoh.*session.*|.*router.*unavailable.*",
            category=ErrorCategory.ZENOH,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RESTART_SERVICE,
            max_retry_attempts=3,
            retry_delay=5.0,
            timeout=60.0
        ))
        
        # Resource exhaustion
        self.register_strategy(RecoveryStrategy(
            error_pattern=r".*out of memory.*|.*disk.*full.*|.*resource.*unavailable.*",
            category=ErrorCategory.RESOURCE,
            severity=ErrorSeverity.CRITICAL,
            action=RecoveryAction.MANUAL_INTERVENTION,
            requires_manual_approval=True
        ))
        
        # Training errors
        self.register_strategy(RecoveryStrategy(
            error_pattern=r".*cuda.*error.*|.*gpu.*|.*training.*failed.*",
            category=ErrorCategory.TRAINING,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RESTART_SERVICE,
            max_retry_attempts=2,
            retry_delay=10.0
        ))
    
    def register_strategy(self, strategy: RecoveryStrategy):
        """Register a new recovery strategy"""
        with self._lock:
            self.recovery_strategies.append(strategy)
            self.logger.info(f"Registered recovery strategy for {strategy.category.value}")
    
    def add_error_callback(self, callback: Callable[[ErrorInfo], None]):
        """Add callback for error notifications"""
        self.error_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[str, RecoveryAction, bool], None]):
        """Add callback for recovery notifications"""
        self.recovery_callbacks.append(callback)
    
    async def handle_error(
        self,
        exception: Exception,
        context: ErrorContext,
        category: ErrorCategory = ErrorCategory.SERVICE,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM
    ) -> str:
        """Handle an error with automatic recovery if enabled"""
        
        error_id = f"{context.service_name}_{int(time.time() * 1000)}_{id(exception)}"
        
        # Create error info
        error_info = ErrorInfo(
            error_id=error_id,
            timestamp=time.time(),
            category=category,
            severity=severity,
            message=str(exception),
            exception_type=type(exception).__name__,
            traceback=traceback.format_exc(),
            context=context
        )
        
        # Store error
        with self._lock:
            self.errors[error_id] = error_info
            self.error_history.append(error_info)
            
            # Keep only last 1000 errors in history
            if len(self.error_history) > 1000:
                self.error_history = self.error_history[-1000:]
        
        # Log error
        self._log_error(error_info)
        
        # Update service health
        self._update_service_health(context.service_name, error_info)
        
        # Notify callbacks
        for callback in self.error_callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        # Attempt recovery if enabled
        if self.enable_recovery:
            await self._attempt_recovery(error_info)
        
        return error_id
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error information"""
        log_message = (
            f"[{error_info.category.value.upper()}] "
            f"{error_info.context.service_name}: {error_info.message}"
        )
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # Log detailed information at debug level
        self.logger.debug(f"Error details: {json.dumps(error_info.__dict__, indent=2, default=str)}")
    
    def _update_service_health(self, service_name: str, error_info: ErrorInfo):
        """Update service health based on error"""
        with self._lock:
            if service_name not in self.service_health:
                self.service_health[service_name] = ServiceHealth(
                    service_name=service_name,
                    status=HealthStatus.HEALTHY,
                    last_check=time.time()
                )
            
            health = self.service_health[service_name]
            health.error_count += 1
            health.last_check = time.time()
            
            # Determine health status based on error severity and count
            if error_info.severity == ErrorSeverity.CRITICAL:
                health.status = HealthStatus.CRITICAL
            elif error_info.severity == ErrorSeverity.HIGH:
                if health.error_count >= 3:
                    health.status = HealthStatus.UNHEALTHY
                else:
                    health.status = HealthStatus.DEGRADED
            elif health.error_count >= 5:
                health.status = HealthStatus.DEGRADED
    
    async def _attempt_recovery(self, error_info: ErrorInfo):
        """Attempt to recover from error using registered strategies"""
        
        # Check if recovery is already in progress for this service
        if self.recovery_in_progress.get(error_info.context.service_name, False):
            self.logger.info(f"Recovery already in progress for {error_info.context.service_name}")
            return
        
        # Find matching recovery strategy
        strategy = self._find_recovery_strategy(error_info)
        if not strategy:
            self.logger.warning(f"No recovery strategy found for error: {error_info.message}")
            return
        
        # Check retry limits
        if error_info.retry_count >= strategy.max_retry_attempts:
            self.logger.error(
                f"Max retry attempts ({strategy.max_retry_attempts}) exceeded for error: {error_info.error_id}"
            )
            return
        
        # Mark recovery in progress
        self.recovery_in_progress[error_info.context.service_name] = True
        
        try:
            # Execute recovery action
            success = await self._execute_recovery_action(strategy, error_info)
            
            # Update error info
            error_info.retry_count += 1
            if success:
                error_info.resolved = True
                error_info.resolution_timestamp = time.time()
                error_info.resolution_method = strategy.action.value
                self.logger.info(f"Successfully recovered from error: {error_info.error_id}")
            
            # Notify recovery callbacks
            for callback in self.recovery_callbacks:
                try:
                    callback(error_info.context.service_name, strategy.action, success)
                except Exception as e:
                    self.logger.error(f"Error in recovery callback: {e}")
        
        except Exception as e:
            self.logger.error(f"Recovery attempt failed: {e}")
        
        finally:
            # Mark recovery complete
            self.recovery_in_progress[error_info.context.service_name] = False
    
    def _find_recovery_strategy(self, error_info: ErrorInfo) -> Optional[RecoveryStrategy]:
        """Find matching recovery strategy for error"""
        import re
        
        for strategy in self.recovery_strategies:
            if (strategy.category == error_info.category and 
                re.search(strategy.error_pattern, error_info.message, re.IGNORECASE)):
                return strategy
        
        return None
    
    async def _execute_recovery_action(self, strategy: RecoveryStrategy, error_info: ErrorInfo) -> bool:
        """Execute recovery action based on strategy"""
        
        self.logger.info(
            f"Executing recovery action {strategy.action.value} for service {error_info.context.service_name}"
        )
        
        try:
            if strategy.action == RecoveryAction.RETRY:
                # Simple retry with delay
                await asyncio.sleep(strategy.retry_delay)
                return True
            
            elif strategy.action == RecoveryAction.RECONNECT:
                # Attempt to reconnect
                return await self._handle_reconnect(error_info, strategy)
            
            elif strategy.action == RecoveryAction.RESTART_SERVICE:
                # Restart service
                return await self._handle_service_restart(error_info, strategy)
            
            elif strategy.action == RecoveryAction.RESET_STATE:
                # Reset service state
                return await self._handle_state_reset(error_info, strategy)
            
            elif strategy.action == RecoveryAction.FALLBACK_MODE:
                # Switch to fallback mode
                return await self._handle_fallback_mode(error_info, strategy)
            
            elif strategy.action == RecoveryAction.MANUAL_INTERVENTION:
                # Requires manual intervention
                return await self._handle_manual_intervention(error_info)
            
            elif strategy.action == RecoveryAction.IGNORE:
                # Ignore the error
                self.logger.info(f"Ignoring error for service {error_info.context.service_name}")
                return True
            
            else:
                self.logger.error(f"Unknown recovery action: {strategy.action}")
                return False
        
        except Exception as e:
            self.logger.error(f"Recovery action failed: {e}")
            return False
    
    async def _handle_reconnect(self, error_info: ErrorInfo, strategy: RecoveryStrategy) -> bool:
        """Handle reconnection recovery"""
        self.logger.info(f"Attempting reconnection for service {error_info.context.service_name}")
        
        # Import here to avoid circular dependency
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
        
        try:
            # Check if there's a session manager available
            if hasattr(error_info.context, 'additional_data') and 'session_manager' in error_info.context.additional_data:
                session_manager = error_info.context.additional_data['session_manager']
                if isinstance(session_manager, EnhancedZenohSessionManager):
                    # Attempt to reconnect Zenoh session
                    await session_manager.reconnect()
                    await asyncio.sleep(strategy.retry_delay)
                    return session_manager.is_connected()
            
            # Generic reconnection attempt
            await asyncio.sleep(strategy.retry_delay)
            self.logger.info(f"Reconnection attempt completed for {error_info.context.service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Reconnection failed for {error_info.context.service_name}: {e}")
            return False
    
    async def _handle_service_restart(self, error_info: ErrorInfo, strategy: RecoveryStrategy) -> bool:
        """Handle service restart recovery"""
        service_name = error_info.context.service_name
        self.logger.info(f"Attempting service restart for {service_name}")
        
        try:
            # Import here to avoid circular dependency
            from src.zenoh_services.core.fault_detection import get_fault_detection_system
            
            fault_detection = get_fault_detection_system()
            service_monitor = fault_detection.get_service_monitor(service_name)
            
            if service_monitor:
                # Stop current monitoring
                await service_monitor.stop_monitoring()
                await asyncio.sleep(2.0)
                
                # Reset service state
                service_monitor.metrics = type(service_monitor.metrics)()
                service_monitor.last_health_status = type(service_monitor.last_health_status).HEALTHY
                
                # Restart monitoring
                await service_monitor.start_monitoring()
                
                self.logger.info(f"Service {service_name} restart completed")
                return True
            else:
                self.logger.warning(f"No service monitor found for {service_name}")
                # Simulate restart delay
                await asyncio.sleep(strategy.retry_delay)
                return True
                
        except Exception as e:
            self.logger.error(f"Service restart failed for {service_name}: {e}")
            return False
    
    async def _handle_state_reset(self, error_info: ErrorInfo, strategy: RecoveryStrategy) -> bool:
        """Handle state reset recovery"""
        service_name = error_info.context.service_name
        self.logger.info(f"Resetting state for service {service_name}")
        
        try:
            # Reset service health status
            with self._lock:
                if service_name in self.service_health:
                    health = self.service_health[service_name]
                    health.error_count = 0
                    health.recovery_attempts = 0
                    health.status = HealthStatus.HEALTHY
                    health.last_check = time.time()
            
            # Clear recent errors for this service
            service_errors = [e for e in self.error_history if e.context.service_name == service_name]
            recent_count = min(10, len(service_errors))
            for error in service_errors[-recent_count:]:
                error.resolved = True
                error.resolution_timestamp = time.time()
                error.resolution_method = "state_reset"
            
            await asyncio.sleep(strategy.retry_delay)
            self.logger.info(f"State reset completed for {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"State reset failed for {service_name}: {e}")
            return False
    
    async def _handle_fallback_mode(self, error_info: ErrorInfo, strategy: RecoveryStrategy) -> bool:
        """Handle fallback mode recovery"""
        service_name = error_info.context.service_name
        self.logger.info(f"Switching to fallback mode for service {service_name}")
        
        try:
            # Set service to fallback mode
            with self._lock:
                if service_name in self.service_health:
                    health = self.service_health[service_name]
                    health.metrics["fallback_mode"] = True
                    health.metrics["fallback_activated"] = time.time()
            
            # For different service types, implement specific fallback logic
            if "training" in service_name.lower():
                await self._training_fallback_mode(service_name)
            elif "simulation" in service_name.lower():
                await self._simulation_fallback_mode(service_name)
            elif "zenoh" in service_name.lower():
                await self._zenoh_fallback_mode(service_name)
            else:
                # Generic fallback
                await self._generic_fallback_mode(service_name)
            
            await asyncio.sleep(strategy.retry_delay)
            self.logger.info(f"Fallback mode activated for {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Fallback mode activation failed for {service_name}: {e}")
            return False
    
    async def _handle_manual_intervention(self, error_info: ErrorInfo) -> bool:
        """Handle manual intervention requirement"""
        service_name = error_info.context.service_name
        
        self.logger.critical(
            f"MANUAL INTERVENTION REQUIRED for service {service_name}: {error_info.message}"
        )
        
        # Try to create notification if notification system is available
        try:
            from src.zenoh_services.core.notification_system import NotificationManager
            # This would require session manager - just log for now
            self.logger.critical(f"Critical error notification should be sent for {service_name}")
        except ImportError:
            pass
        
        # Mark service as requiring manual intervention
        with self._lock:
            if service_name in self.service_health:
                health = self.service_health[service_name]
                health.status = HealthStatus.CRITICAL
                health.metrics["manual_intervention_required"] = True
                health.metrics["intervention_requested"] = time.time()
        
        return False  # Manual intervention always requires human action
    
    async def _training_fallback_mode(self, service_name: str):
        """Fallback mode for training services"""
        self.logger.info(f"Training service {service_name} fallback: reducing batch size and learning rate")
        # In real implementation, this would communicate with the training service
    
    async def _simulation_fallback_mode(self, service_name: str):
        """Fallback mode for simulation services"""
        self.logger.info(f"Simulation service {service_name} fallback: switching to simplified physics")
        # In real implementation, this would reduce simulation complexity
    
    async def _zenoh_fallback_mode(self, service_name: str):
        """Fallback mode for Zenoh services"""
        self.logger.info(f"Zenoh service {service_name} fallback: switching to local mode")
        # In real implementation, this would switch to offline/local operation
    
    async def _generic_fallback_mode(self, service_name: str):
        """Generic fallback mode"""
        self.logger.info(f"Generic fallback mode for service {service_name}: reduced functionality")
        # In real implementation, this would disable non-critical features
    
    def get_service_health(self, service_name: str) -> Optional[ServiceHealth]:
        """Get health status for a service"""
        with self._lock:
            return self.service_health.get(service_name)
    
    def get_all_service_health(self) -> Dict[str, ServiceHealth]:
        """Get health status for all services"""
        with self._lock:
            return self.service_health.copy()
    
    def get_error_history(self, 
                         service_name: Optional[str] = None,
                         category: Optional[ErrorCategory] = None,
                         severity: Optional[ErrorSeverity] = None,
                         limit: int = 100) -> List[ErrorInfo]:
        """Get error history with optional filters"""
        with self._lock:
            filtered_errors = self.error_history
            
            if service_name:
                filtered_errors = [e for e in filtered_errors if e.context.service_name == service_name]
            
            if category:
                filtered_errors = [e for e in filtered_errors if e.category == category]
            
            if severity:
                filtered_errors = [e for e in filtered_errors if e.severity == severity]
            
            # Return most recent errors first
            return list(reversed(filtered_errors[-limit:]))
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        with self._lock:
            stats = {
                "total_errors": len(self.error_history),
                "errors_by_category": {},
                "errors_by_severity": {},
                "errors_by_service": {},
                "resolved_errors": 0,
                "recovery_success_rate": 0.0
            }
            
            for error in self.error_history:
                # By category
                category = error.category.value
                stats["errors_by_category"][category] = stats["errors_by_category"].get(category, 0) + 1
                
                # By severity
                severity = error.severity.value
                stats["errors_by_severity"][severity] = stats["errors_by_severity"].get(severity, 0) + 1
                
                # By service
                service = error.context.service_name
                stats["errors_by_service"][service] = stats["errors_by_service"].get(service, 0) + 1
                
                # Resolved errors
                if error.resolved:
                    stats["resolved_errors"] += 1
            
            # Calculate success rate
            if stats["total_errors"] > 0:
                stats["recovery_success_rate"] = stats["resolved_errors"] / stats["total_errors"]
            
            return stats
    
    async def shutdown(self):
        """Shutdown error handler"""
        self.logger.info("Shutting down error handler...")
        
        # Cancel all recovery tasks
        for task in self.recovery_tasks.values():
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self.recovery_tasks:
            await asyncio.gather(*self.recovery_tasks.values(), return_exceptions=True)
        
        self.logger.info("Error handler shutdown complete")


# Global error handler instance
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get global error handler instance"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def initialize_error_handler(log_file: Optional[str] = None, enable_recovery: bool = True) -> ErrorHandler:
    """Initialize global error handler"""
    global _global_error_handler
    _global_error_handler = ErrorHandler(log_file, enable_recovery)
    return _global_error_handler


# Convenient decorator for error handling
def handle_errors(
    category: ErrorCategory = ErrorCategory.SERVICE,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    service_name: Optional[str] = None
):
    """Decorator for automatic error handling"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    context = ErrorContext(
                        service_name=service_name or func.__module__,
                        service_state="unknown",
                        timestamp=time.time(),
                        thread_id=threading.get_ident(),
                        function_name=func.__name__
                    )
                    error_handler = get_error_handler()
                    await error_handler.handle_error(e, context, category, severity)
                    raise
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    context = ErrorContext(
                        service_name=service_name or func.__module__,
                        service_state="unknown", 
                        timestamp=time.time(),
                        thread_id=threading.get_ident(),
                        function_name=func.__name__
                    )
                    error_handler = get_error_handler()
                    # For sync functions, we can't await, so just log
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(error_handler.handle_error(e, context, category, severity))
                    loop.close()
                    raise
            return sync_wrapper
    return decorator