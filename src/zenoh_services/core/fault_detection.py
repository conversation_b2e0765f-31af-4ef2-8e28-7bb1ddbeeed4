"""
Service Fault Detection and Auto-Recovery System
Monitors service health and automatically recovers from failures.
"""

import asyncio
import logging
import time
import psutil
import threading
from typing import Dict, Any, Optional, Callable, List, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json

from .error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Error<PERSON>ate<PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, ErrorContext, 
    HealthStatus, ServiceHealth, get_error_handler
)


class FailureType(Enum):
    """Types of service failures"""
    UNRESPONSIVE = "unresponsive"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    CONNECTION_LOST = "connection_lost"
    PERFORMANCE_DEGRADED = "performance_degraded"
    CRASH = "crash"
    MEMORY_LEAK = "memory_leak"
    HIGH_ERROR_RATE = "high_error_rate"


@dataclass
class HealthCheck:
    """Health check configuration"""
    name: str
    check_function: Callable[[], bool]
    interval: float = 30.0  # seconds
    timeout: float = 10.0   # seconds
    failure_threshold: int = 3
    success_threshold: int = 2
    enabled: bool = True
    last_check: Optional[float] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0


@dataclass
class ServiceMetrics:
    """Service performance metrics"""
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    response_time: float = 0.0
    error_rate: float = 0.0
    requests_per_second: float = 0.0
    active_connections: int = 0
    last_updated: float = 0.0


@dataclass
class RecoveryAction:
    """Recovery action configuration"""
    action_name: str
    action_function: Callable[[], bool]
    timeout: float = 60.0
    max_attempts: int = 3
    delay_between_attempts: float = 5.0
    requires_confirmation: bool = False


class ServiceMonitor:
    """Monitors individual service health and performance"""
    
    def __init__(self, service_name: str, error_handler: ErrorHandler):
        self.service_name = service_name
        self.error_handler = error_handler
        self.logger = logging.getLogger(f"service_monitor.{service_name}")
        
        # Health checks
        self.health_checks: Dict[str, HealthCheck] = {}
        
        # Metrics
        self.metrics = ServiceMetrics()
        self.metrics_history: List[ServiceMetrics] = []
        
        # Recovery actions
        self.recovery_actions: Dict[FailureType, List[RecoveryAction]] = {}
        
        # State
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.last_health_status = HealthStatus.HEALTHY
        
        # Thresholds
        self.cpu_threshold = 80.0  # %
        self.memory_threshold = 90.0  # %
        self.response_time_threshold = 5.0  # seconds
        self.error_rate_threshold = 0.1  # 10%
    
    def add_health_check(self, health_check: HealthCheck):
        """Add a health check"""
        self.health_checks[health_check.name] = health_check
        self.logger.info(f"Added health check: {health_check.name}")
    
    def add_recovery_action(self, failure_type: FailureType, action: RecoveryAction):
        """Add recovery action for specific failure type"""
        if failure_type not in self.recovery_actions:
            self.recovery_actions[failure_type] = []
        self.recovery_actions[failure_type].append(action)
        self.logger.info(f"Added recovery action {action.action_name} for {failure_type.value}")
    
    async def start_monitoring(self):
        """Start service monitoring"""
        if self.is_monitoring:
            self.logger.warning("Monitoring already started")
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info(f"Started monitoring service: {self.service_name}")
    
    async def stop_monitoring(self):
        """Stop service monitoring"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info(f"Stopped monitoring service: {self.service_name}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        try:
            while self.is_monitoring:
                # Update metrics
                await self._update_metrics()
                
                # Run health checks
                health_status = await self._run_health_checks()
                
                # Detect failures
                failures = self._detect_failures()
                
                # Handle failures
                for failure_type in failures:
                    await self._handle_failure(failure_type)
                
                # Update health status
                if health_status != self.last_health_status:
                    self.logger.info(
                        f"Health status changed from {self.last_health_status.value} to {health_status.value}"
                    )
                    self.last_health_status = health_status
                
                # Wait for next check
                await asyncio.sleep(5.0)  # Check every 5 seconds
                
        except asyncio.CancelledError:
            self.logger.info("Monitoring loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in monitoring loop: {e}")
            await self.error_handler.handle_error(
                e,
                ErrorContext(
                    service_name=self.service_name,
                    service_state="monitoring",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="_monitoring_loop"
                ),
                ErrorCategory.SERVICE,
                ErrorSeverity.HIGH
            )
    
    async def _update_metrics(self):
        """Update service metrics"""
        try:
            # Get system metrics
            self.metrics.cpu_usage = psutil.cpu_percent()
            self.metrics.memory_usage = psutil.virtual_memory().percent
            self.metrics.last_updated = time.time()
            
            # Store metrics history (keep last 100 entries)
            self.metrics_history.append(self.metrics)
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
                
        except Exception as e:
            self.logger.error(f"Error updating metrics: {e}")
    
    async def _run_health_checks(self) -> HealthStatus:
        """Run all health checks and determine overall health status"""
        overall_status = HealthStatus.HEALTHY
        failed_checks = 0
        total_checks = len(self.health_checks)
        
        for check_name, health_check in self.health_checks.items():
            if not health_check.enabled:
                continue
            
            # Check if it's time to run this health check
            current_time = time.time()
            if (health_check.last_check and 
                current_time - health_check.last_check < health_check.interval):
                continue
            
            try:
                # Run health check with timeout
                import sys
                if sys.version_info >= (3, 9):
                    success = await asyncio.wait_for(
                        asyncio.to_thread(health_check.check_function),
                        timeout=health_check.timeout
                    )
                else:
                    # Fallback for Python < 3.9
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        success = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                executor, health_check.check_function
                            ),
                            timeout=health_check.timeout
                        )
                
                health_check.last_check = current_time
                
                if success:
                    health_check.consecutive_failures = 0
                    health_check.consecutive_successes += 1
                else:
                    health_check.consecutive_successes = 0
                    health_check.consecutive_failures += 1
                    failed_checks += 1
                    
                    if health_check.consecutive_failures >= health_check.failure_threshold:
                        self.logger.warning(
                            f"Health check {check_name} failed {health_check.consecutive_failures} times"
                        )
                        
            except asyncio.TimeoutError:
                health_check.consecutive_failures += 1
                failed_checks += 1
                self.logger.error(f"Health check {check_name} timed out")
                
            except Exception as e:
                health_check.consecutive_failures += 1
                failed_checks += 1
                self.logger.error(f"Health check {check_name} failed with error: {e}")
        
        # Determine overall health status
        if total_checks == 0:
            return HealthStatus.HEALTHY
        
        failure_rate = failed_checks / total_checks
        if failure_rate >= 0.5:
            overall_status = HealthStatus.CRITICAL
        elif failure_rate >= 0.3:
            overall_status = HealthStatus.UNHEALTHY
        elif failure_rate > 0:
            overall_status = HealthStatus.DEGRADED
        
        return overall_status
    
    def _detect_failures(self) -> List[FailureType]:
        """Detect different types of failures"""
        failures = []
        
        # Resource exhaustion
        if (self.metrics.cpu_usage > self.cpu_threshold or 
            self.metrics.memory_usage > self.memory_threshold):
            failures.append(FailureType.RESOURCE_EXHAUSTION)
        
        # Performance degradation
        if self.metrics.response_time > self.response_time_threshold:
            failures.append(FailureType.PERFORMANCE_DEGRADED)
        
        # High error rate
        if self.metrics.error_rate > self.error_rate_threshold:
            failures.append(FailureType.HIGH_ERROR_RATE)
        
        # Memory leak detection (simple heuristic)
        if len(self.metrics_history) >= 10:
            recent_memory = [m.memory_usage for m in self.metrics_history[-10:]]
            if all(recent_memory[i] < recent_memory[i+1] for i in range(9)):
                # Memory consistently increasing
                failures.append(FailureType.MEMORY_LEAK)
        
        return failures
    
    async def _handle_failure(self, failure_type: FailureType):
        """Handle detected failure"""
        self.logger.warning(f"Detected failure: {failure_type.value}")
        
        # Get recovery actions for this failure type
        actions = self.recovery_actions.get(failure_type, [])
        if not actions:
            self.logger.warning(f"No recovery actions defined for {failure_type.value}")
            return
        
        # Execute recovery actions
        for action in actions:
            try:
                success = await self._execute_recovery_action(action)
                if success:
                    self.logger.info(f"Recovery action {action.action_name} succeeded")
                    break
                else:
                    self.logger.warning(f"Recovery action {action.action_name} failed")
            except Exception as e:
                self.logger.error(f"Error executing recovery action {action.action_name}: {e}")
    
    async def _execute_recovery_action(self, action: RecoveryAction) -> bool:
        """Execute a recovery action with retries"""
        for attempt in range(action.max_attempts):
            try:
                self.logger.info(
                    f"Executing recovery action {action.action_name} (attempt {attempt + 1}/{action.max_attempts})"
                )
                
                # Execute action with timeout
                import sys
                if sys.version_info >= (3, 9):
                    success = await asyncio.wait_for(
                        asyncio.to_thread(action.action_function),
                        timeout=action.timeout
                    )
                else:
                    # Fallback for Python < 3.9
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        success = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                executor, action.action_function
                            ),
                            timeout=action.timeout
                        )
                
                if success:
                    return True
                
            except asyncio.TimeoutError:
                self.logger.error(f"Recovery action {action.action_name} timed out")
            except Exception as e:
                self.logger.error(f"Recovery action {action.action_name} failed: {e}")
            
            # Wait before next attempt
            if attempt < action.max_attempts - 1:
                await asyncio.sleep(action.delay_between_attempts)
        
        return False
    
    def get_metrics(self) -> ServiceMetrics:
        """Get current service metrics"""
        return self.metrics
    
    def get_metrics_history(self) -> List[ServiceMetrics]:
        """Get metrics history"""
        return self.metrics_history.copy()


class FaultDetectionSystem:
    """Central fault detection and recovery system"""
    
    def __init__(self, error_handler: Optional[ErrorHandler] = None):
        self.error_handler = error_handler or get_error_handler()
        self.logger = logging.getLogger("fault_detection_system")
        
        # Service monitors
        self.service_monitors: Dict[str, ServiceMonitor] = {}
        
        # Global health checks
        self.global_health_checks: Dict[str, HealthCheck] = {}
        
        # System-wide monitoring
        self.system_monitoring = False
        self.system_monitoring_task: Optional[asyncio.Task] = None
        
        # Notification callbacks
        self.failure_callbacks: List[Callable[[str, FailureType], None]] = []
        self.recovery_callbacks: List[Callable[[str, str, bool], None]] = []
        
        # Configure default health checks
        self._setup_default_health_checks()
    
    def _setup_default_health_checks(self):
        """Setup default system-wide health checks"""
        
        # System resource checks
        self.add_global_health_check(HealthCheck(
            name="system_cpu",
            check_function=lambda: psutil.cpu_percent() < 90.0,
            interval=30.0
        ))
        
        self.add_global_health_check(HealthCheck(
            name="system_memory", 
            check_function=lambda: psutil.virtual_memory().percent < 95.0,
            interval=30.0
        ))
        
        self.add_global_health_check(HealthCheck(
            name="system_disk",
            check_function=lambda: psutil.disk_usage('/').percent < 95.0,
            interval=60.0
        ))
    
    def register_service(self, service_name: str) -> ServiceMonitor:
        """Register a new service for monitoring"""
        if service_name in self.service_monitors:
            return self.service_monitors[service_name]
        
        monitor = ServiceMonitor(service_name, self.error_handler)
        self.service_monitors[service_name] = monitor
        
        self.logger.info(f"Registered service for monitoring: {service_name}")
        return monitor
    
    def add_global_health_check(self, health_check: HealthCheck):
        """Add a global health check"""
        self.global_health_checks[health_check.name] = health_check
        self.logger.info(f"Added global health check: {health_check.name}")
    
    def add_failure_callback(self, callback: Callable[[str, FailureType], None]):
        """Add callback for failure notifications"""
        self.failure_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[str, str, bool], None]):
        """Add callback for recovery notifications"""
        self.recovery_callbacks.append(callback)
    
    async def start_system_monitoring(self):
        """Start system-wide monitoring"""
        if self.system_monitoring:
            self.logger.warning("System monitoring already started")
            return
        
        self.system_monitoring = True
        
        # Start all service monitors
        for monitor in self.service_monitors.values():
            await monitor.start_monitoring()
        
        # Start global monitoring
        self.system_monitoring_task = asyncio.create_task(self._system_monitoring_loop())
        
        self.logger.info("Started system-wide monitoring")
    
    async def stop_system_monitoring(self):
        """Stop system-wide monitoring"""
        if not self.system_monitoring:
            return
        
        self.system_monitoring = False
        
        # Stop all service monitors
        for monitor in self.service_monitors.values():
            await monitor.stop_monitoring()
        
        # Stop global monitoring
        if self.system_monitoring_task:
            self.system_monitoring_task.cancel()
            try:
                await self.system_monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stopped system-wide monitoring")
    
    async def _system_monitoring_loop(self):
        """System-wide monitoring loop"""
        try:
            while self.system_monitoring:
                # Run global health checks
                await self._run_global_health_checks()
                
                # Check for system-wide issues
                await self._check_system_health()
                
                # Generate health summary
                await self._generate_health_summary()
                
                # Wait for next check
                await asyncio.sleep(30.0)  # Check every 30 seconds
                
        except asyncio.CancelledError:
            self.logger.info("System monitoring loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in system monitoring loop: {e}")
    
    async def _run_global_health_checks(self):
        """Run global health checks"""
        for check_name, health_check in self.global_health_checks.items():
            if not health_check.enabled:
                continue
            
            current_time = time.time()
            if (health_check.last_check and 
                current_time - health_check.last_check < health_check.interval):
                continue
            
            try:
                import sys
                if sys.version_info >= (3, 9):
                    success = await asyncio.wait_for(
                        asyncio.to_thread(health_check.check_function),
                        timeout=health_check.timeout
                    )
                else:
                    # Fallback for Python < 3.9
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        success = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                executor, health_check.check_function
                            ),
                            timeout=health_check.timeout
                        )
                
                health_check.last_check = current_time
                
                if not success:
                    health_check.consecutive_failures += 1
                    if health_check.consecutive_failures >= health_check.failure_threshold:
                        self.logger.warning(f"Global health check {check_name} failing")
                else:
                    health_check.consecutive_failures = 0
                    
            except Exception as e:
                self.logger.error(f"Global health check {check_name} failed: {e}")
    
    async def _check_system_health(self):
        """Check overall system health"""
        unhealthy_services = []
        
        for service_name, monitor in self.service_monitors.items():
            if monitor.last_health_status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
                unhealthy_services.append(service_name)
        
        if unhealthy_services:
            self.logger.warning(f"Unhealthy services detected: {unhealthy_services}")
    
    async def _generate_health_summary(self):
        """Generate system health summary"""
        summary = {
            "timestamp": time.time(),
            "overall_health": "healthy",
            "services": {},
            "global_checks": {},
            "system_metrics": {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }
        
        # Service health
        critical_services = 0
        for service_name, monitor in self.service_monitors.items():
            health_status = monitor.last_health_status.value
            summary["services"][service_name] = {
                "status": health_status,
                "metrics": monitor.get_metrics().__dict__
            }
            
            if monitor.last_health_status == HealthStatus.CRITICAL:
                critical_services += 1
        
        # Global health checks
        for check_name, health_check in self.global_health_checks.items():
            summary["global_checks"][check_name] = {
                "consecutive_failures": health_check.consecutive_failures,
                "last_check": health_check.last_check
            }
        
        # Overall health assessment
        if critical_services > 0:
            summary["overall_health"] = "critical"
        elif any(s["status"] in ["unhealthy", "degraded"] for s in summary["services"].values()):
            summary["overall_health"] = "degraded"
        
        # Log summary at debug level
        self.logger.debug(f"Health summary: {json.dumps(summary, indent=2)}")
    
    def get_service_monitor(self, service_name: str) -> Optional[ServiceMonitor]:
        """Get service monitor"""
        return self.service_monitors.get(service_name)
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get current system health status"""
        return {
            "services": {
                name: {
                    "status": monitor.last_health_status.value,
                    "metrics": monitor.get_metrics().__dict__
                }
                for name, monitor in self.service_monitors.items()
            },
            "global_checks": {
                name: {
                    "consecutive_failures": check.consecutive_failures,
                    "last_check": check.last_check
                }
                for name, check in self.global_health_checks.items()
            },
            "system_metrics": {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }
    
    async def shutdown(self):
        """Shutdown fault detection system"""
        self.logger.info("Shutting down fault detection system...")
        await self.stop_system_monitoring()
        self.logger.info("Fault detection system shutdown complete")


# Global fault detection system instance
_global_fault_detection: Optional[FaultDetectionSystem] = None


def get_fault_detection_system() -> FaultDetectionSystem:
    """Get global fault detection system"""
    global _global_fault_detection
    if _global_fault_detection is None:
        _global_fault_detection = FaultDetectionSystem()
    return _global_fault_detection