#!/bin/bash

# EngineAI Legged Gym 自动安装脚本
# 使用方法: bash install.sh

set -e  # 遇到错误时退出

echo "============================================================"
echo "EngineAI Legged Gym 自动安装脚本"
echo "============================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    print_status "检查前置条件..."

    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        print_error "此脚本仅支持Linux系统"
        exit 1
    fi

    # 检查NVIDIA驱动
    if ! command -v nvidia-smi &> /dev/null; then
        print_error "未找到nvidia-smi，请确保已安装NVIDIA驱动"
        exit 1
    fi

    # 检查Conda
    if ! command -v conda &> /dev/null; then
        print_error "未找到conda，请先安装Miniconda或Anaconda"
        print_error "下载地址: https://docs.conda.io/en/latest/miniconda.html"
        exit 1
    fi

    # 检查Isaac Gym目录
    if [ ! -d "isaacgym" ]; then
        print_error "未找到isaacgym目录，请确保在项目根目录运行此脚本"
        exit 1
    fi

    print_status "前置条件检查通过 ✓"
}

# 创建Conda环境
create_conda_env() {
    print_status "创建Conda环境..."

    ENV_NAME="engineai_legged_gym_py38"

    # 检查环境是否已存在
    if conda env list | grep -q "$ENV_NAME"; then
        print_warning "环境 $ENV_NAME 已存在"
        read -p "是否删除并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "删除现有环境..."
            conda env remove -n "$ENV_NAME" -y
        else
            print_status "使用现有环境"
            return 0
        fi
    fi

    print_status "创建Python 3.8环境..."
    conda create -n "$ENV_NAME" python=3.8 -y

    print_status "Conda环境创建完成 ✓"
}

# 安装PyTorch
install_pytorch() {
    print_status "安装PyTorch和CUDA支持..."

    # 激活环境并安装PyTorch
    eval "$(conda shell.bash hook)"
    conda activate engineai_legged_gym_py38

    pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1 \
        --extra-index-url https://download.pytorch.org/whl/cu117

    print_status "PyTorch安装完成 ✓"
}

# 安装Isaac Gym
install_isaac_gym() {
    print_status "安装Isaac Gym..."

    eval "$(conda shell.bash hook)"
    conda activate engineai_legged_gym_py38

    cd isaacgym/python
    pip install -e .
    cd ../..

    print_status "Isaac Gym安装完成 ✓"
}

# 安装项目依赖
install_dependencies() {
    print_status "安装项目依赖..."

    eval "$(conda shell.bash hook)"
    conda activate engineai_legged_gym_py38

    pip install -r requirements.txt

    print_status "项目依赖安装完成 ✓"
}

# 安装项目本身
install_project() {
    print_status "安装项目..."

    eval "$(conda shell.bash hook)"
    conda activate engineai_legged_gym_py38

    pip install -e .

    print_status "项目安装完成 ✓"
}

# 验证安装
verify_installation() {
    print_status "验证安装..."

    eval "$(conda shell.bash hook)"
    conda activate engineai_legged_gym_py38

    if python test_environment.py; then
        print_status "安装验证成功 ✓"
        return 0
    else
        print_error "安装验证失败"
        return 1
    fi
}

# 主安装流程
main() {
    print_status "开始安装 EngineAI Legged Gym..."

    check_prerequisites
    create_conda_env
    install_pytorch
    install_isaac_gym
    install_dependencies
    install_project

    echo
    echo "============================================================"
    print_status "安装完成！"
    echo "============================================================"
    echo
    print_status "验证安装..."

    if verify_installation; then
        echo
        echo "🎉 安装成功！"
        echo
        echo "使用方法:"
        echo "1. 激活环境: conda activate engineai_legged_gym_py38"
        echo "2. 运行测试: python test_environment.py"
        echo "3. 开始训练: python scripts/train.py --task=zqsa01 --headless"
        echo
    else
        echo
        print_error "安装过程中出现问题，请检查错误信息"
        echo "请参考 INSTALLATION_GUIDE.md 进行手动安装"
        exit 1
    fi
}

# 错误处理
trap 'print_error "安装过程中出现错误，请检查上面的错误信息"' ERR

# 运行主函数
main "$@"