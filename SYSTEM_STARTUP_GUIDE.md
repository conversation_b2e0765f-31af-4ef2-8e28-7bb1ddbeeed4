# EngineAI 系统启动指南

本指南介绍如何使用 EngineAI 足式机器人训练系统的一键启动脚本。

## 🚀 启动脚本概览

我们提供了两个主要的启动脚本：

1. **`quick_start.py`** - 快速启动和验证脚本（推荐初次使用）
2. **`bin/start_complete_system.py`** - 完整系统启动脚本（高级用户）

## 📋 使用前准备

### 1. 激活Conda环境
```bash
conda activate engineai_legged_gym_py38
```

### 2. 进入项目目录
```bash
cd /path/to/engineai_legged_gym
```

### 3. 构建Web应用（首次使用）
```bash
python quick_start.py --build-web
```

## 🏃‍♂️ 快速启动（推荐）

### 基本使用
```bash
# 快速启动核心服务
python quick_start.py
```

这将启动：
- ✅ Zenoh路由器 (端口7447)
- ✅ WebSocket后端 (端口8080) 
- ✅ Web前端服务器 (端口3000)

### 其他选项
```bash
# 仅验证系统功能（不启动服务）
python quick_start.py --verify-only

# 构建Web应用
python quick_start.py --build-web

# 显示帮助信息
python quick_start.py --help
```

### 启动成功后
访问 **http://localhost:3000** 打开Web管理界面。

## 🔧 完整系统启动（高级）

### 启动所有服务
```bash
# 启动完整系统（所有服务）
python bin/start_complete_system.py
```

这将启动以下服务：
- ✅ Zenoh路由器
- ✅ 配置服务
- ✅ 训练服务
- ✅ 仿真服务
- ✅ 部署服务
- ✅ 演示服务
- ✅ WebSocket后端
- ✅ Web前端

### 自定义启动选项

#### 最小化启动
```bash
# 仅启动Web界面和必要服务
python bin/start_complete_system.py --minimal
```

#### 启动指定服务
```bash
# 启动特定服务
python bin/start_complete_system.py --services zenoh_router training_service web_backend web_frontend
```

#### 不启动Web界面
```bash
# 仅启动后端服务
python bin/start_complete_system.py --no-web
```

### 系统诊断命令

#### 检查依赖
```bash
# 仅检查系统依赖，不启动服务
python bin/start_complete_system.py --check-only
```

#### 查看状态
```bash
# 显示当前系统状态
python bin/start_complete_system.py --status
```

## 🌐 Web界面访问

启动成功后，访问以下地址：

- **Web管理界面**: http://localhost:3000
- **WebSocket API**: ws://localhost:8080
- **Zenoh路由器**: tcp://localhost:7447

### Web界面功能
- 📊 **仪表盘**: 系统状态监控
- 🏋️‍♂️ **模型训练**: 启动和监控训练任务
- 🎮 **仿真环境**: 机器人控制和仿真
- 📦 **模型部署**: 导出和部署模型
- 🔍 **系统监控**: 错误监控和性能监控

## 🎯 训练任务启动

### 通过Web界面启动（推荐）
1. 访问 http://localhost:3000
2. 点击"模型训练"页面
3. 配置训练参数
4. 点击"开始训练"

### 通过命令行启动
```bash
# 基础训练
python scripts/train.py --task=zqsa01

# 自定义参数训练
python scripts/train.py --task=zqsa01 \
    --num_envs=4096 \
    --max_iterations=1500 \
    --headless
```

## 🛑 停止系统

### 停止方法
1. **Ctrl+C** - 在启动脚本的终端中按下
2. **关闭终端** - 直接关闭运行启动脚本的终端

系统会自动优雅地停止所有服务。

### 强制停止（如果需要）
```bash
# 查找并终止相关进程
pkill -f "zenoh_router"
pkill -f "start_web"
pkill -f "serve_web"
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```
❌ Port 3000 is already in use
```
**解决方案**：
```bash
# 查看占用端口的进程
netstat -tulpn | grep :3000

# 终止占用进程
sudo fuser -k 3000/tcp

# 或使用不同端口
python scripts/serve_web.py --port 3001
```

#### 2. Conda环境问题
```
❌ 请先激活正确的Conda环境
```
**解决方案**：
```bash
conda activate engineai_legged_gym_py38
```

#### 3. 缺少依赖
```
❌ 缺少依赖: torch, zenoh
```
**解决方案**：
```bash
pip install -r requirements.txt
```

#### 4. Isaac Gym未安装
```
❌ Isaac Gym
```
**解决方案**：
1. 从NVIDIA下载Isaac Gym Preview 4
2. 解压并安装：
```bash
cd isaacgym/python && pip install -e .
```

#### 5. Web应用未构建
```
❌ Web应用未构建
```
**解决方案**：
```bash
python quick_start.py --build-web
```

### 检查系统健康状态
```bash
# 验证系统功能
python quick_start.py --verify-only

# 检查依赖
python bin/start_complete_system.py --check-only
```

## 📈 性能建议

### 系统要求
- **最低配置**: 16GB RAM, GTX 1060, Intel i5-8400
- **推荐配置**: 32GB RAM, RTX 3070+, Intel i7-10700K+

### 优化建议
1. **使用无头模式训练**：`--headless` 参数可显著提升性能
2. **调整环境数量**：根据GPU内存调整 `--num_envs` 参数
3. **关闭不需要的服务**：使用 `--services` 参数仅启动必要服务

## 🔄 开发模式

### 开发Web界面
```bash
# 启动后端服务
python bin/start_complete_system.py --services zenoh_router web_backend

# 在另一个终端启动前端开发服务器
cd web && npm run dev
```

### 调试模式
```bash
# 启动时显示详细日志
export PYTHONPATH=$PYTHONPATH:$(pwd)/src
python bin/start_complete_system.py --services zenoh_router training_service
```

## 📞 获取帮助

- 📖 **文档**: 查看 `README.md` 和 `docs/` 目录
- 🐛 **问题报告**: [GitHub Issues](https://github.com/engineai-robotics/engineai_legged_gym/issues)
- 💬 **讨论**: [GitHub Discussions](https://github.com/engineai-robotics/engineai_legged_gym/discussions)
- 📧 **联系邮箱**: <EMAIL>

---

**祝您使用愉快！如果遇到问题，请参考故障排除部分或联系我们的技术支持。** 