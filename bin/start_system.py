#!/usr/bin/env python3
"""
One-click system startup script for EngineAI Legged Gym.
Starts all necessary services in the correct order.
"""

import os
import sys
import time
import signal
import subprocess
import argparse
import threading
from pathlib import Path
from typing import List, Dict, Optional


class ServiceManager:
    """Manages multiple services with proper startup and shutdown."""
    
    def __init__(self):
        self.services: Dict[str, subprocess.Popen] = {}
        self.running = False
        
    def start_service(self, name: str, command: List[str], cwd: Optional[str] = None) -> bool:
        """Start a service."""
        try:
            print(f"🚀 Starting {name}...")
            process = subprocess.Popen(
                command,
                cwd=cwd or os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.services[name] = process
            
            # Give the service a moment to start
            time.sleep(2)
            
            # Check if it's still running
            if process.poll() is None:
                print(f"✅ {name} started successfully (PID: {process.pid})")
                return True
            else:
                print(f"❌ {name} failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting {name}: {e}")
            return False
    
    def stop_service(self, name: str) -> bool:
        """Stop a service."""
        if name not in self.services:
            return True
            
        process = self.services[name]
        if process.poll() is None:  # Still running
            print(f"🛑 Stopping {name}...")
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {name} stopped")
            except subprocess.TimeoutExpired:
                print(f"⚠️  Force killing {name}...")
                process.kill()
                process.wait()
            except Exception as e:
                print(f"❌ Error stopping {name}: {e}")
                return False
        
        del self.services[name]
        return True
    
    def stop_all(self):
        """Stop all services."""
        print("\n🛑 Shutting down all services...")
        self.running = False
        
        # Stop services in reverse order
        service_names = list(self.services.keys())
        for name in reversed(service_names):
            self.stop_service(name)
    
    def monitor_services(self):
        """Monitor services and restart if needed."""
        while self.running:
            time.sleep(5)
            
            for name, process in list(self.services.items()):
                if process.poll() is not None:  # Process has terminated
                    print(f"⚠️  {name} has stopped unexpectedly")
                    # Could implement restart logic here
    
    def wait_for_services(self):
        """Wait for all services to complete."""
        try:
            while self.running and self.services:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🔴 Received interrupt signal")
            self.stop_all()


def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    # Check Python packages
    required_packages = ['zenoh', 'websockets', 'torch']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    # Check if web app is built
    dist_path = Path("zenoh_services/web_gui/dist")
    if not dist_path.exists() or not (dist_path / "index.html").exists():
        print("❌ Web app not built")
        print("Please build it with: cd zenoh_services/web_gui && npm run build")
        return False
    else:
        print("  ✅ Web app built")
    
    print("✅ All dependencies satisfied")
    return True


def start_full_system(web_port: int = 3000, websocket_port: int = 8080):
    """Start the complete system."""
    if not check_dependencies():
        return False
    
    manager = ServiceManager()
    manager.running = True
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print(f"\n🔴 Received signal {signum}")
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start services in order
        services_started = 0
        
        # 1. Start Web Backend (WebSocket bridge)
        if manager.start_service(
            "Web Backend",
            [sys.executable, "start_web_backend.py"]
        ):
            services_started += 1
        
        # 2. Start Web Frontend Server
        if manager.start_service(
            "Web Frontend",
            [sys.executable, "scripts/serve_web.py", "--port", str(web_port), "--no-browser"]
        ):
            services_started += 1
        
        if services_started == 0:
            print("❌ No services started successfully")
            return False
        
        # Print status
        print("\n" + "="*60)
        print("🎉 EngineAI Legged Gym System Started!")
        print("="*60)
        print(f"📱 Web Interface: http://localhost:{web_port}")
        print(f"🔗 WebSocket API: ws://localhost:{websocket_port}")
        print("📊 System Status: All services running")
        print("\n💡 Usage:")
        print("  - Open the web interface to monitor and control training")
        print("  - Use demo_services.py to start additional services")
        print("  - Press Ctrl+C to stop all services")
        print("="*60)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=manager.monitor_services, daemon=True)
        monitor_thread.start()
        
        # Wait for services
        manager.wait_for_services()
        
        return True
        
    except Exception as e:
        print(f"❌ Error starting system: {e}")
        manager.stop_all()
        return False


def start_minimal_system():
    """Start minimal system (just web interface)."""
    print("🚀 Starting minimal system (Web interface only)...")
    
    if not check_dependencies():
        return False
    
    # Just start the web server
    try:
        subprocess.run([
            sys.executable, "scripts/serve_web.py", "--port", "3000"
        ])
        return True
    except KeyboardInterrupt:
        print("\n🛑 System stopped")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Start EngineAI Legged Gym System")
    parser.add_argument('--minimal', action='store_true', help='Start minimal system (web only)')
    parser.add_argument('--web-port', type=int, default=3000, help='Web interface port')
    parser.add_argument('--websocket-port', type=int, default=8080, help='WebSocket API port')
    parser.add_argument('--check-only', action='store_true', help='Only check dependencies')
    
    args = parser.parse_args()
    
    if args.check_only:
        success = check_dependencies()
        sys.exit(0 if success else 1)
    
    print("🚀 EngineAI Legged Gym System Startup")
    print("="*50)
    
    if args.minimal:
        success = start_minimal_system()
    else:
        success = start_full_system(args.web_port, args.websocket_port)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
