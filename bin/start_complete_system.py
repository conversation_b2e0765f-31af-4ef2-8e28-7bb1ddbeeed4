#!/usr/bin/env python3
"""
EngineAI 足式机器人训练系统 - 完整系统启动脚本
一键启动所有必要的服务，包括Zenoh路由器、训练服务、仿真服务、Web界面等

使用方法:
    python bin/start_complete_system.py                # 启动所有服务
    python bin/start_complete_system.py --minimal      # 最小化启动（仅Web界面）
    python bin/start_complete_system.py --services training simulation  # 启动指定服务
"""

import os
import sys
import time
import signal
import subprocess
import argparse
import threading
import asyncio
import json
import psutil
from pathlib import Path
from typing import List, Dict, Optional, Set
from datetime import datetime

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SystemManager:
    """EngineAI系统管理器 - 管理所有服务的启动、监控和关闭"""
    
    def __init__(self):
        self.services: Dict[str, subprocess.Popen] = {}
        self.running = False
        self.startup_order = [
            "zenoh_router",
            "config_service", 
            "training_service",
            "simulation_service",
            "deployment_service",
            "play_service",
            "web_backend",
            "web_frontend"
        ]
        
        # 服务配置
        self.service_configs = {
            "zenoh_router": {
                "command": [sys.executable, "src/zenoh_services/zenoh_router.py"],
                "health_check": self._check_zenoh_health,
                "startup_time": 5,
                "description": "Zenoh消息路由器"
            },
            "config_service": {
                "command": [sys.executable, "-m", "src.zenoh_services.services", "config_service"],
                "health_check": None,
                "startup_time": 3,
                "description": "配置服务"
            },
            "training_service": {
                "command": [sys.executable, "-m", "src.zenoh_services.services", "training_service"],
                "health_check": None,
                "startup_time": 8,
                "description": "训练服务"
            },
            "simulation_service": {
                "command": [sys.executable, "-m", "src.zenoh_services.services", "simulation_service"],
                "health_check": None,
                "startup_time": 5,
                "description": "仿真服务"
            },
            "deployment_service": {
                "command": [sys.executable, "-m", "src.zenoh_services.services", "deployment_service"],
                "health_check": None,
                "startup_time": 3,
                "description": "部署服务"
            },
            "play_service": {
                "command": [sys.executable, "src/zenoh_services/services/play_service.py"],
                "health_check": None,
                "startup_time": 5,
                "description": "演示服务"
            },
            "web_backend": {
                "command": [sys.executable, "bin/start_web.py"],
                "health_check": self._check_websocket_health,
                "startup_time": 3,
                "description": "WebSocket后端"
            },
            "web_frontend": {
                "command": [sys.executable, "scripts/serve_web.py", "--port", "3000", "--no-browser"],
                "health_check": self._check_web_frontend_health,
                "startup_time": 2,
                "description": "Web前端服务器"
            }
        }
    
    def check_dependencies(self) -> bool:
        """检查系统依赖"""
        logger.info("🔍 检查系统依赖...")
        
        missing_deps = []
        
        # 检查Python包
        required_packages = [
            'torch', 'zenoh', 'websockets', 'psutil', 'numpy'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"  ✅ {package}")
            except ImportError:
                missing_deps.append(package)
                logger.error(f"  ❌ {package}")
        
        # 检查Isaac Gym
        try:
            from isaacgym import gymapi
            logger.info("  ✅ Isaac Gym")
        except ImportError:
            missing_deps.append("Isaac Gym")
            logger.error("  ❌ Isaac Gym")
        
        # 检查Web应用构建
        web_dist = Path("web/dist")
        if web_dist.exists() and (web_dist / "index.html").exists():
            logger.info("  ✅ Web应用已构建")
        else:
            logger.warning("  ⚠️  Web应用未构建 - 运行: cd web && npm run build")
        
        # 检查系统资源
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 16:
            logger.warning(f"  ⚠️  内存不足 ({memory_gb:.1f}GB < 16GB推荐)")
        else:
            logger.info(f"  ✅ 内存充足 ({memory_gb:.1f}GB)")
        
        if missing_deps:
            logger.error(f"❌ 缺少依赖: {', '.join(missing_deps)}")
            logger.error("请运行: pip install -r requirements.txt")
            return False
        
        logger.info("✅ 所有依赖检查通过")
        return True
    
    def _check_zenoh_health(self) -> bool:
        """检查Zenoh路由器健康状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', 7447))
            sock.close()
            return result == 0
        except:
            return False
    
    def _check_websocket_health(self) -> bool:
        """检查WebSocket后端健康状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', 8080))
            sock.close()
            return result == 0
        except:
            return False
    
    def _check_web_frontend_health(self) -> bool:
        """检查Web前端健康状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', 3000))
            sock.close()
            return result == 0
        except:
            return False
    
    def start_service(self, name: str) -> bool:
        """启动单个服务"""
        if name in self.services:
            logger.warning(f"服务 {name} 已运行")
            return True
        
        if name not in self.service_configs:
            logger.error(f"未知服务: {name}")
            return False
        
        config = self.service_configs[name]
        logger.info(f"🚀 启动 {config['description']} ({name})...")
        
        try:
            process = subprocess.Popen(
                config["command"],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.services[name] = process
            
            # 等待服务启动
            time.sleep(config["startup_time"])
            
            # 检查进程状态
            if process.poll() is None:
                # 如果有健康检查，执行健康检查
                if config["health_check"]:
                    if config["health_check"]():
                        logger.info(f"✅ {config['description']} 启动成功 (PID: {process.pid})")
                        return True
                    else:
                        logger.error(f"❌ {config['description']} 健康检查失败")
                        return False
                else:
                    logger.info(f"✅ {config['description']} 启动成功 (PID: {process.pid})")
                    return True
            else:
                logger.error(f"❌ {config['description']} 启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动 {name} 时出错: {e}")
            return False
    
    def stop_service(self, name: str) -> bool:
        """停止单个服务"""
        if name not in self.services:
            return True
        
        config = self.service_configs.get(name, {})
        description = config.get('description', name)
        
        process = self.services[name]
        if process.poll() is None:  # 仍在运行
            logger.info(f"🛑 停止 {description}...")
            try:
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"✅ {description} 已停止")
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️  强制结束 {description}...")
                process.kill()
                process.wait()
            except Exception as e:
                logger.error(f"❌ 停止 {name} 时出错: {e}")
                return False
        
        del self.services[name]
        return True
    
    def stop_all_services(self):
        """停止所有服务"""
        logger.info("🛑 停止所有服务...")
        self.running = False
        
        # 按相反顺序停止服务
        for name in reversed(self.startup_order):
            if name in self.services:
                self.stop_service(name)
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            time.sleep(10)  # 每10秒检查一次
            
            failed_services = []
            for name, process in list(self.services.items()):
                if process.poll() is not None:  # 进程已终止
                    failed_services.append(name)
            
            if failed_services:
                logger.warning(f"⚠️  以下服务意外停止: {', '.join(failed_services)}")
                # 可以在这里实现自动重启逻辑
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "running_services": [],
            "failed_services": [],
            "system_health": "healthy"
        }
        
        for name, process in self.services.items():
            config = self.service_configs.get(name, {})
            service_info = {
                "name": name,
                "description": config.get('description', name),
                "pid": process.pid,
                "status": "running" if process.poll() is None else "stopped"
            }
            
            if service_info["status"] == "running":
                status["running_services"].append(service_info)
            else:
                status["failed_services"].append(service_info)
        
        if status["failed_services"]:
            status["system_health"] = "degraded"
        
        return status
    
    def print_status(self):
        """打印系统状态"""
        status = self.get_system_status()
        
        logger.info("📊 系统状态报告:")
        logger.info(f"   时间: {status['timestamp']}")
        logger.info(f"   健康状态: {status['system_health']}")
        logger.info(f"   运行中服务: {len(status['running_services'])}")
        logger.info(f"   失败服务: {len(status['failed_services'])}")
        
        if status["running_services"]:
            logger.info("   🟢 运行中的服务:")
            for service in status["running_services"]:
                logger.info(f"      - {service['description']} (PID: {service['pid']})")
        
        if status["failed_services"]:
            logger.info("   🔴 失败的服务:")
            for service in status["failed_services"]:
                logger.info(f"      - {service['description']}")
    
    def start_all_services(self, selected_services: Optional[List[str]] = None):
        """启动所有服务或指定服务"""
        if not self.check_dependencies():
            logger.error("❌ 依赖检查失败，无法启动系统")
            return False
        
        self.running = True
        services_to_start = selected_services or self.startup_order
        
        logger.info("🚀 启动 EngineAI 足式机器人训练系统...")
        logger.info("="*60)
        
        # 启动服务
        started_services = []
        for service_name in services_to_start:
            if service_name in self.service_configs:
                if self.start_service(service_name):
                    started_services.append(service_name)
                else:
                    logger.error(f"❌ 启动 {service_name} 失败")
                    # 继续启动其他服务，不要因为一个服务失败就停止
        
        if not started_services:
            logger.error("❌ 没有服务启动成功")
            return False
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_services, daemon=True)
        monitor_thread.start()
        
        # 打印启动摘要
        logger.info("="*60)
        logger.info("🎉 EngineAI 系统启动完成!")
        logger.info("="*60)
        
        if "web_frontend" in started_services:
            logger.info("📱 Web界面: http://localhost:3000")
        if "web_backend" in started_services:
            logger.info("🔗 WebSocket API: ws://localhost:8080")
        if "zenoh_router" in started_services:
            logger.info("📡 Zenoh路由器: tcp://localhost:7447")
        
        logger.info("")
        logger.info("💡 使用说明:")
        logger.info("   - 打开Web界面进行训练监控和控制")
        logger.info("   - 按 Ctrl+C 停止所有服务")
        logger.info("   - 运行 'python scripts/train.py --task=zqsa01' 开始训练")
        logger.info("="*60)
        
        self.print_status()
        
        return True
    
    def wait_for_shutdown(self):
        """等待关闭信号"""
        try:
            while self.running and self.services:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("\n🔴 收到中断信号")
            self.stop_all_services()


def setup_signal_handlers(manager: SystemManager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"\n🔴 收到信号 {signum}")
        manager.stop_all_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main():
    """主入口函数"""
    parser = argparse.ArgumentParser(
        description="EngineAI 足式机器人训练系统 - 完整启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python bin/start_complete_system.py                    # 启动所有服务
  python bin/start_complete_system.py --minimal          # 最小化启动
  python bin/start_complete_system.py --services training simulation web_backend web_frontend
  python bin/start_complete_system.py --check-only       # 仅检查依赖
  python bin/start_complete_system.py --status           # 显示状态
        """
    )
    
    parser.add_argument('--minimal', action='store_true', 
                       help='最小化启动（仅Web界面和必要服务）')
    parser.add_argument('--services', nargs='+', 
                       help='启动指定服务',
                       choices=['zenoh_router', 'config_service', 'training_service', 
                               'simulation_service', 'deployment_service', 'play_service',
                               'web_backend', 'web_frontend'])
    parser.add_argument('--check-only', action='store_true', 
                       help='仅检查依赖，不启动服务')
    parser.add_argument('--status', action='store_true', 
                       help='显示系统状态')
    parser.add_argument('--no-web', action='store_true', 
                       help='不启动Web界面')
    
    args = parser.parse_args()
    
    manager = SystemManager()
    
    # 仅检查依赖
    if args.check_only:
        success = manager.check_dependencies()
        sys.exit(0 if success else 1)
    
    # 显示状态
    if args.status:
        manager.print_status()
        sys.exit(0)
    
    # 设置信号处理器
    setup_signal_handlers(manager)
    
    logger.info("🚀 EngineAI 足式机器人训练系统")
    logger.info("="*50)
    
    try:
        # 确定要启动的服务
        if args.minimal:
            # 最小化启动
            services_to_start = ["zenoh_router", "web_backend", "web_frontend"]
        elif args.services:
            # 用户指定的服务
            services_to_start = args.services
        elif args.no_web:
            # 不启动Web界面
            services_to_start = ["zenoh_router", "config_service", "training_service", 
                               "simulation_service", "deployment_service"]
        else:
            # 启动所有服务
            services_to_start = None
        
        # 启动系统
        success = manager.start_all_services(services_to_start)
        
        if success:
            # 等待关闭
            manager.wait_for_shutdown()
        else:
            logger.error("❌ 系统启动失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()
        manager.stop_all_services()
        sys.exit(1)
    
    logger.info("✅ 系统已完全关闭")
    sys.exit(0)


if __name__ == "__main__":
    main() 