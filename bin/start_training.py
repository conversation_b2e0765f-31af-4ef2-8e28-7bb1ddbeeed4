#!/usr/bin/env python3
"""
Simple training service starter for EngineAI Legged Gym
"""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Start training service"""
    try:
        logger.info("🚀 Starting EngineAI Training Service...")

        # Import here to avoid early Isaac Gym loading
        from src.zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        from src.zenoh_services.services.training_service import create_training_service, TrainingServiceConfig

        # Create Zenoh session manager
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )

        session_manager = EnhancedZenohSessionManager(zenoh_config, "training_service")

        # Initialize session
        logger.info("Initializing Zenoh session...")
        await session_manager.initialize()

        # Create and initialize training service
        logger.info("Creating training service...")
        training_config = TrainingServiceConfig(
            task_name="anymal_c_flat",
            num_envs=4096,
            max_iterations=1500,
            experiment_name="web_training",
            run_name="default_run"
        )

        training_service = await create_training_service(session_manager, training_config)

        logger.info("✅ Training service started successfully")
        logger.info("   - Zenoh session connected")
        logger.info("   - Training service initialized")
        logger.info("   - Ready to receive training commands")

        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Training service interrupted")
    except Exception as e:
        logger.error(f"Training service error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        if 'training_service' in locals():
            await training_service.shutdown()
        if 'session_manager' in locals():
            await session_manager.shutdown()
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
