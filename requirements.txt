# EngineAI Legged Gym - Python Dependencies
# Core ML and RL dependencies
torch>=1.13.0,<2.0.0
torchvision>=0.14.0,<1.0.0
torchaudio>=0.13.0,<1.0.0
numpy==1.23.*
matplotlib>=3.5.0

# <PERSON> (manual installation required)
# Download from: https://developer.nvidia.com/isaac-gym
# isaacgym

# RL Algorithm
# rsl-rl (included in project)

# Computer Vision
opencv-python>=4.5.0
pygame>=2.1.0

# Physics Simulation
mujoco>=2.3.0,<3.0.0
mujoco-python-viewer>=0.1.4

# Distributed Computing and Messaging
eclipse-zenoh>=0.10.0
msgpack>=1.0.0
websockets>=10.0

# Data Processing and Utilities
tqdm>=4.64.0
psutil>=5.9.0
tensorboard>=2.10.0

# Development and Testing
pytest>=7.0.0
black>=22.0.0
isort>=5.10.0
flake8>=5.0.0
pre-commit>=2.20.0

# System utilities
setuptools==59.5.0
packaging>=21.0

# Optional: Performance monitoring
nvidia-ml-py3>=7.352.0

# Optional: Jupyter notebook support
jupyter>=1.0.0
ipywidgets>=7.6.0

# Optional: Experiment tracking
wandb>=0.13.0

# Web backend dependencies
fastapi>=0.85.0
uvicorn>=0.18.0
pydantic>=1.10.0

# Additional utilities
pyyaml>=6.0
click>=8.0.0
rich>=12.0.0
