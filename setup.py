from setuptools import find_packages, setup
import os

# Read the contents of README file
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

# Read requirements from requirements.txt
def read_requirements():
    """Read requirements from requirements.txt file."""
    requirements = []
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-r'):
                    requirements.append(line)
    except FileNotFoundError:
        # Fallback to hardcoded requirements if file doesn't exist
        requirements = [
            'torch>=1.13.0,<2.0.0',
            'torchvision>=0.14.0,<1.0.0',
            'torchaudio>=0.13.0,<1.0.0',
            'numpy==1.23.*',
            'matplotlib>=3.5.0',
            'opencv-python>=4.5.0',
            'pygame>=2.1.0',
            'mujoco>=2.3.0',
            'mujoco-python-viewer>=1.0.0',
            'eclipse-zenoh>=0.10.0',
            'msgpack>=1.0.0',
            'websockets>=10.0',
            'tqdm>=4.64.0',
            'psutil>=5.9.0',
            'tensorboard>=2.10.0',
            'setuptools==59.5.0',
            'packaging>=21.0',
            'pyyaml>=6.0',
            'click>=8.0.0',
            'rich>=12.0.0'
        ]
    return requirements

setup(
    name='engineai_training',
    version='1.0.0',
    author='engineai',
    author_email='<EMAIL>',
    description='Isaac Gym environments for Legged Robots with Zenoh integration',
    long_description=long_description,
    long_description_content_type='text/markdown',
    url='https://github.com/engineai-robotics/engineai_legged_gym',
    license="BSD-3-Clause",
    packages=find_packages(),
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Science/Research',
        'License :: OSI Approved :: BSD License',
        'Operating System :: POSIX :: Linux',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ],
    python_requires='>=3.8,<3.11',
    install_requires=read_requirements(),
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'black>=22.0.0',
            'isort>=5.10.0',
            'flake8>=5.0.0',
            'mypy>=0.991',
            'pre-commit>=2.20.0',
        ],
        'monitoring': [
            'nvidia-ml-py3>=7.352.0',
            'prometheus-client>=0.14.0',
        ],
        'jupyter': [
            'jupyter>=1.0.0',
            'ipywidgets>=7.6.0',
        ],
        'wandb': [
            'wandb>=0.13.0',
        ],
    },
    entry_points={
        'console_scripts': [
            'engineai-train=scripts.train:main',
            'engineai-play=scripts.play:main',
            'engineai-demo=bin.demo_services:main',
        ],
    },
    include_package_data=True,
    package_data={
        'src.legged_gym': ['envs/*/config/*.yaml'],
        'src.zenoh_services': ['config/*.yaml'],
        'resources': ['**/*'],
    },
)
