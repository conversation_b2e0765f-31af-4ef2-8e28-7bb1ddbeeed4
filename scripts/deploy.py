#!/usr/bin/env python3
"""
Deployment script for EngineAI Legged Gym.
Handles different deployment scenarios: local, docker, production.
"""

import os
import sys
import subprocess
import argparse
import shutil
from pathlib import Path
from typing import List, Dict, Optional


class DeploymentManager:
    """Manages deployment of the EngineAI Legged Gym system."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.deployment_configs = {
            'local': self.deploy_local,
            'docker': self.deploy_docker,
            'production': self.deploy_production
        }
    
    def check_prerequisites(self, deployment_type: str) -> bool:
        """Check deployment prerequisites."""
        print(f"🔍 Checking prerequisites for {deployment_type} deployment...")
        
        common_checks = [
            ('Python 3.8+', self._check_python),
            ('Git', self._check_git),
            ('Project structure', self._check_project_structure)
        ]
        
        type_specific_checks = {
            'docker': [
                ('Docker', self._check_docker),
                ('Docker Compose', self._check_docker_compose)
            ],
            'production': [
                ('Systemd', self._check_systemd),
                ('Nginx (optional)', self._check_nginx)
            ]
        }
        
        all_checks = common_checks + type_specific_checks.get(deployment_type, [])
        
        passed = 0
        for name, check_func in all_checks:
            if check_func():
                print(f"  ✅ {name}")
                passed += 1
            else:
                print(f"  ❌ {name}")
        
        success = passed == len(all_checks)
        if success:
            print("✅ All prerequisites satisfied")
        else:
            print(f"❌ {len(all_checks) - passed} prerequisites failed")
        
        return success
    
    def _check_python(self) -> bool:
        """Check Python version."""
        try:
            import sys
            version_info = sys.version_info
            return version_info.major == 3 and version_info.minor >= 8
        except:
            return False
    
    def _check_git(self) -> bool:
        """Check Git availability."""
        try:
            subprocess.run(['git', '--version'], capture_output=True, check=True)
            return True
        except:
            return False
    
    def _check_docker(self) -> bool:
        """Check Docker availability."""
        try:
            subprocess.run(['docker', '--version'], capture_output=True, check=True)
            return True
        except:
            return False
    
    def _check_docker_compose(self) -> bool:
        """Check Docker Compose availability."""
        try:
            subprocess.run(['docker-compose', '--version'], capture_output=True, check=True)
            return True
        except:
            # Try docker compose (newer syntax)
            try:
                subprocess.run(['docker', 'compose', 'version'], capture_output=True, check=True)
                return True
            except:
                return False
    
    def _check_systemd(self) -> bool:
        """Check systemd availability."""
        return Path('/etc/systemd/system').exists()
    
    def _check_nginx(self) -> bool:
        """Check nginx availability."""
        try:
            subprocess.run(['nginx', '-v'], capture_output=True, check=True)
            return True
        except:
            return False
    
    def _check_project_structure(self) -> bool:
        """Check project structure."""
        required_paths = [
            'legged_gym',
            'zenoh_services',
            'requirements.txt',
            'setup.py'
        ]
        
        return all((self.project_root / path).exists() for path in required_paths)
    
    def deploy_local(self) -> bool:
        """Deploy for local development."""
        print("🚀 Starting local deployment...")
        
        try:
            # Create virtual environment if it doesn't exist
            venv_path = self.project_root / 'venv'
            if not venv_path.exists():
                print("📦 Creating virtual environment...")
                subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], check=True)
            
            # Install dependencies
            pip_path = venv_path / 'bin' / 'pip'
            if not pip_path.exists():
                pip_path = venv_path / 'Scripts' / 'pip.exe'  # Windows
            
            print("📦 Installing dependencies...")
            subprocess.run([str(pip_path), 'install', '-r', 'requirements.txt'], 
                         cwd=self.project_root, check=True)
            
            # Install project in development mode
            subprocess.run([str(pip_path), 'install', '-e', '.'], 
                         cwd=self.project_root, check=True)
            
            # Build web interface
            web_gui_path = self.project_root / 'zenoh_services' / 'web_gui'
            if web_gui_path.exists():
                print("🌐 Building web interface...")
                subprocess.run(['npm', 'install'], cwd=web_gui_path, check=True)
                subprocess.run(['npm', 'run', 'build'], cwd=web_gui_path, check=True)
            
            # Create necessary directories
            for dir_name in ['logs', 'data', 'models']:
                (self.project_root / dir_name).mkdir(exist_ok=True)
            
            print("✅ Local deployment completed successfully!")
            print(f"💡 To start the system: python start_system.py")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Local deployment failed: {e}")
            return False
    
    def deploy_docker(self) -> bool:
        """Deploy using Docker."""
        print("🐳 Starting Docker deployment...")
        
        try:
            # Build Docker image
            print("🔨 Building Docker image...")
            subprocess.run(['docker', 'build', '-t', 'engineai-legged-gym:latest', '.'], 
                         cwd=self.project_root, check=True)
            
            # Start services with docker-compose
            print("🚀 Starting services...")
            subprocess.run(['docker-compose', 'up', '-d'], 
                         cwd=self.project_root, check=True)
            
            print("✅ Docker deployment completed successfully!")
            print("💡 Services:")
            print("  - Web Interface: http://localhost:3000")
            print("  - WebSocket API: ws://localhost:8080")
            print("  - Zenoh Router: tcp://localhost:7447")
            print("💡 To stop: docker-compose down")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Docker deployment failed: {e}")
            return False
    
    def deploy_production(self) -> bool:
        """Deploy for production."""
        print("🏭 Starting production deployment...")
        
        try:
            # Create production directories
            prod_dirs = [
                '/opt/engineai-legged-gym',
                '/var/log/engineai-legged-gym',
                '/etc/engineai-legged-gym'
            ]
            
            for dir_path in prod_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            
            # Copy application files
            print("📁 Copying application files...")
            app_dir = Path('/opt/engineai-legged-gym')
            
            # Copy main application
            for item in ['legged_gym', 'zenoh_services', 'rsl_rl', 'scripts']:
                src = self.project_root / item
                if src.exists():
                    if src.is_dir():
                        shutil.copytree(src, app_dir / item, dirs_exist_ok=True)
                    else:
                        shutil.copy2(src, app_dir / item)
            
            # Copy configuration files
            for item in ['requirements.txt', 'setup.py', 'start_system.py']:
                src = self.project_root / item
                if src.exists():
                    shutil.copy2(src, app_dir / item)
            
            # Create systemd service
            self._create_systemd_service()
            
            # Create nginx configuration (if nginx is available)
            if self._check_nginx():
                self._create_nginx_config()
            
            print("✅ Production deployment completed successfully!")
            print("💡 To start: sudo systemctl start engineai-legged-gym")
            print("💡 To enable auto-start: sudo systemctl enable engineai-legged-gym")
            return True
            
        except Exception as e:
            print(f"❌ Production deployment failed: {e}")
            return False
    
    def _create_systemd_service(self):
        """Create systemd service file."""
        service_content = """[Unit]
Description=EngineAI Legged Gym System
After=network.target

[Service]
Type=simple
User=engineai
Group=engineai
WorkingDirectory=/opt/engineai-legged-gym
Environment=PYTHONPATH=/opt/engineai-legged-gym
ExecStart=/opt/engineai-legged-gym/venv/bin/python start_system.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_path = Path('/etc/systemd/system/engineai-legged-gym.service')
        service_path.write_text(service_content)
        
        # Reload systemd
        subprocess.run(['sudo', 'systemctl', 'daemon-reload'], check=True)
        print("✅ Systemd service created")
    
    def _create_nginx_config(self):
        """Create nginx configuration."""
        nginx_content = """server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""
        
        nginx_path = Path('/etc/nginx/sites-available/engineai-legged-gym')
        nginx_path.write_text(nginx_content)
        
        # Enable site
        enabled_path = Path('/etc/nginx/sites-enabled/engineai-legged-gym')
        if not enabled_path.exists():
            enabled_path.symlink_to(nginx_path)
        
        print("✅ Nginx configuration created")
    
    def deploy(self, deployment_type: str) -> bool:
        """Main deployment method."""
        if deployment_type not in self.deployment_configs:
            print(f"❌ Unknown deployment type: {deployment_type}")
            return False
        
        if not self.check_prerequisites(deployment_type):
            return False
        
        return self.deployment_configs[deployment_type]()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Deploy EngineAI Legged Gym")
    parser.add_argument('type', choices=['local', 'docker', 'production'], 
                       help='Deployment type')
    parser.add_argument('--check-only', action='store_true', 
                       help='Only check prerequisites')
    
    args = parser.parse_args()
    
    manager = DeploymentManager()
    
    if args.check_only:
        success = manager.check_prerequisites(args.type)
        sys.exit(0 if success else 1)
    
    print(f"🚀 EngineAI Legged Gym Deployment ({args.type})")
    print("="*50)
    
    success = manager.deploy(args.type)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
